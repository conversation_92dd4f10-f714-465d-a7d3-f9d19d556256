#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Главный скрипт для внедрения JavaScript мониторинга чата в Battle.net
через Chrome DevTools Protocol
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# Импорт наших модулей
from battlenet_message_server import MessageServer
from battlenet_cdp_client import BattleNetCDPClient

class BattleNetChatInjector:
    def __init__(self):
        self.server = MessageServer()
        self.cdp_client = BattleNetCDPClient()
        self.js_code = None
        self.running = False
        
    def load_javascript_code(self):
        """Загрузка JavaScript кода из файла"""
        js_file = Path("chat_monitor.js")
        
        if not js_file.exists():
            print("❌ Файл chat_monitor.js не найден!")
            return False
        
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                self.js_code = f.read()
            print("✅ JavaScript код загружен")
            return True
        except Exception as e:
            print(f"❌ Ошибка загрузки JavaScript: {e}")
            return False
    
    def start_server(self):
        """Запуск HTTP сервера"""
        print("🚀 Запуск HTTP сервера...")
        if self.server.start():
            print("✅ HTTP сервер запущен")
            return True
        else:
            print("❌ Не удалось запустить HTTP сервер")
            return False
    
    async def inject_monitor(self):
        """Внедрение мониторинга чата"""
        print("🔌 Подключение к Battle.net...")
        
        if not await self.cdp_client.connect():
            print("❌ Не удалось подключиться к Battle.net")
            return False
        
        print("💉 Внедрение JavaScript мониторинга...")
        
        if not await self.cdp_client.inject_javascript(self.js_code):
            print("❌ Не удалось внедрить JavaScript")
            return False
        
        print("✅ JavaScript мониторинг успешно внедрен!")
        return True
    
    async def run(self):
        """Основной цикл работы"""
        print("=" * 60)
        print("🎮 Battle.net Chat Injector")
        print("=" * 60)
        
        # Загрузка JavaScript кода
        if not self.load_javascript_code():
            return False
        
        # Запуск HTTP сервера
        if not self.start_server():
            return False
        
        # Внедрение мониторинга
        if not await self.inject_monitor():
            return False
        
        print("\n" + "=" * 60)
        print("🎉 Система успешно запущена!")
        print("=" * 60)
        print("📝 Сообщения сохраняются в: battlenet_messages.txt")
        print("📊 Статус сервера: http://localhost:8080/status")
        print("🔧 Для остановки нажмите Ctrl+C")
        print("=" * 60)
        
        self.running = True
        
        try:
            # Основной цикл мониторинга
            while self.running:
                await asyncio.sleep(1)
                
                # Проверка состояния сервера
                if not self.server.is_running():
                    print("⚠️ HTTP сервер остановлен, перезапуск...")
                    self.server.start()
                
                # Проверка подключения к Battle.net
                if not self.cdp_client.connected:
                    print("⚠️ Потеряно подключение к Battle.net, переподключение...")
                    await self.cdp_client.connect()
                    if self.cdp_client.connected:
                        await self.cdp_client.inject_javascript(self.js_code)
        
        except KeyboardInterrupt:
            print("\n🛑 Получен сигнал остановки...")
        
        finally:
            await self.cleanup()
        
        return True
    
    async def cleanup(self):
        """Очистка ресурсов"""
        print("🧹 Очистка ресурсов...")
        
        self.running = False
        
        # Остановка CDP клиента
        if self.cdp_client.connected:
            await self.cdp_client.disconnect()
        
        # Остановка HTTP сервера
        if self.server.is_running():
            self.server.stop()
        
        print("✅ Очистка завершена")


def print_requirements():
    """Вывод требований для работы"""
    print("\n📋 Требования для работы:")
    print("1. Battle.net должен быть установлен")
    print("2. Установите зависимости: pip install websockets aiohttp psutil")
    print("3. Убедитесь, что порт 8080 свободен")
    print("4. Файлы chat_monitor.js должен быть в той же папке")


def check_dependencies():
    """Проверка зависимостей"""
    required_modules = ['websockets', 'aiohttp', 'psutil']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ Отсутствуют зависимости:")
        for module in missing_modules:
            print(f"   - {module}")
        print(f"\n💡 Установите: pip install {' '.join(missing_modules)}")
        return False
    
    return True


async def main():
    """Главная функция"""
    print("🔍 Проверка зависимостей...")
    
    if not check_dependencies():
        print_requirements()
        return
    
    # Проверка наличия файлов
    required_files = ['chat_monitor.js', 'battlenet_message_server.py', 'battlenet_cdp_client.py']
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print("❌ Отсутствуют файлы:")
        for file in missing_files:
            print(f"   - {file}")
        return
    
    # Запуск инжектора
    injector = BattleNetChatInjector()
    await injector.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 До свидания!")
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()
        print_requirements()
