#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP сервер для приема сообщений от JavaScript из Battle.net
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import threading
import time
from datetime import datetime
import os
import urllib.parse

class ChatMessageHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.messages_file = "battlenet_messages.txt"
        super().__init__(*args, **kwargs)
    
    def do_POST(self):
        """Обработка POST запросов с сообщениями чата"""
        if self.path == '/chat-message':
            try:
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    message_data = json.loads(post_data.decode('utf-8'))

                    # Сохранение сообщения
                    self.save_message(message_data)

                    # Отправка успешного ответа
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    self.end_headers()

                    response = {"status": "success", "message": "Message saved"}
                    self.wfile.write(json.dumps(response).encode('utf-8'))
                else:
                    self.send_error(400, "No content")

            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                self.send_error(400, f"Invalid JSON: {e}")
            except Exception as e:
                print(f"Server error: {e}")
                self.send_error(500, f"Server error: {e}")

        elif self.path == '/send-message':
            try:
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    send_data = json.loads(post_data.decode('utf-8'))

                    # Обработка запроса на отправку сообщения
                    result = self.handle_send_message(send_data)

                    # Отправка ответа
                    self.send_response(200 if result['success'] else 400)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    self.end_headers()

                    self.wfile.write(json.dumps(result).encode('utf-8'))
                else:
                    self.send_error(400, "No content")

            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                self.send_error(400, f"Invalid JSON: {e}")
            except Exception as e:
                print(f"Server error: {e}")
                self.send_error(500, f"Server error: {e}")

        else:
            self.send_error(404, "Not found")
    
    def do_OPTIONS(self):
        """Обработка CORS preflight запросов"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_GET(self):
        """Обработка GET запросов"""
        if self.path == '/status':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            status_html = f"""
            <html>
            <head><title>Battle.net Message Server</title></head>
            <body>
                <h1>Battle.net Message Server</h1>
                <p>Сервер работает: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Файл сообщений: {self.messages_file}</p>
                <p>Существует: {'Да' if os.path.exists(self.messages_file) else 'Нет'}</p>
            </body>
            </html>
            """
            self.wfile.write(status_html.encode('utf-8'))

        elif self.path.startswith('/get-commands'):
            # Обработка запроса команд отправки
            try:
                # Извлекаем chatId из параметров запроса
                if '?' in self.path:
                    params = urllib.parse.parse_qs(self.path.split('?')[1])
                    chat_id = params.get('chatId', [''])[0]
                else:
                    chat_id = ''

                commands = self.get_send_commands(chat_id)

                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()

                self.wfile.write(json.dumps(commands).encode('utf-8'))

            except Exception as e:
                print(f"Ошибка получения команд: {e}")
                self.send_error(500, f"Server error: {e}")
        else:
            self.send_error(404, "Not found")
    
    def save_message(self, data):
        """Сохранение сообщения в файл"""
        try:
            text = data.get('text', '').strip()
            sender = data.get('sender')
            timestamp = data.get('timestamp')  # Battle.net время
            capture_time = data.get('captureTime', int(time.time() * 1000))
            is_rollup = data.get('isRollup', False)
            msg_id = data.get('msgId', '')
            chat_id = data.get('chatId', 'unknown')

            if not text:
                return

            # Фильтрация дублирующихся сообщений по ID
            if msg_id and self.is_duplicate_message_by_id(msg_id):
                return

            # Форматирование времени захвата
            dt = datetime.fromtimestamp(capture_time / 1000)
            capture_time_str = dt.strftime('%Y-%m-%d %H:%M:%S')

            # Форматирование сообщения для сохранения
            chat_prefix = f"[Chat:{chat_id}]" if chat_id != 'unknown' else ""

            if sender and timestamp:
                # Полное сообщение с отправителем и временем
                formatted_msg = f"[{capture_time_str}] {chat_prefix} {sender} ({timestamp}): {text}"
            elif is_rollup:
                # Rollup сообщение (продолжение от того же пользователя)
                formatted_msg = f"[{capture_time_str}] {chat_prefix} [продолжение]: {text}"
            else:
                # Сообщение без метаданных
                formatted_msg = f"[{capture_time_str}] {chat_prefix} {text}"

            # Запись в файл
            with open(self.messages_file, 'a', encoding='utf-8') as f:
                f.write(f"{formatted_msg}\n")

            print(f"💬 Сохранено: {formatted_msg}")

        except Exception as e:
            print(f"Ошибка сохранения: {e}")

    def is_duplicate_message_by_id(self, msg_id):
        """Проверка на дублирующиеся сообщения по ID"""
        if not os.path.exists(self.messages_file) or not msg_id:
            return False

        try:
            with open(self.messages_file, 'r', encoding='utf-8') as f:
                content = f.read()
                return msg_id in content
        except:
            pass

        return False
    
    def is_duplicate_message(self, text):
        """Проверка на дублирующиеся сообщения"""
        if not os.path.exists(self.messages_file):
            return False
        
        try:
            with open(self.messages_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # Проверяем последние 10 строк
                for line in lines[-10:]:
                    if text in line:
                        return True
        except:
            pass
        
        return False
    
    def handle_send_message(self, data):
        """Обработка запроса на отправку сообщения"""
        try:
            message_text = data.get('message', '').strip()
            chat_id = data.get('chatId', '')

            if not message_text:
                return {"success": False, "error": "Empty message"}

            if not chat_id:
                return {"success": False, "error": "Chat ID not specified"}

            # Сохраняем команду отправки в специальный файл
            command_data = {
                "action": "send_message",
                "chatId": chat_id,
                "message": message_text,
                "timestamp": time.time()
            }

            # Записываем команду в файл для обработки JavaScript
            with open('send_commands.json', 'a', encoding='utf-8') as f:
                f.write(json.dumps(command_data) + '\n')

            print(f"📤 Команда отправки: [{chat_id}] {message_text}")

            return {
                "success": True,
                "message": f"Send command queued for chat {chat_id}",
                "chatId": chat_id,
                "text": message_text
            }

        except Exception as e:
            print(f"Ошибка обработки команды отправки: {e}")
            return {"success": False, "error": str(e)}

    def get_send_commands(self, chat_id):
        """Получение команд отправки для указанного чата"""
        commands = []
        commands_file = 'send_commands.json'

        if not os.path.exists(commands_file):
            return commands

        try:
            # Читаем все команды
            with open(commands_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Фильтруем команды для указанного чата
            remaining_lines = []
            for line in lines:
                line = line.strip()
                if line:
                    try:
                        command = json.loads(line)
                        if command.get('chatId') == chat_id:
                            commands.append(command)
                        else:
                            # Сохраняем команды для других чатов
                            remaining_lines.append(line)
                    except json.JSONDecodeError:
                        # Сохраняем некорректные строки
                        remaining_lines.append(line)

            # Перезаписываем файл без обработанных команд
            with open(commands_file, 'w', encoding='utf-8') as f:
                for line in remaining_lines:
                    f.write(line + '\n')

        except Exception as e:
            print(f"Ошибка чтения команд: {e}")

        return commands

    def log_message(self, format, *args):
        """Отключение стандартного логирования запросов"""
        pass


class MessageServer:
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
    
    def start(self):
        """Запуск сервера"""
        try:
            self.server = HTTPServer((self.host, self.port), ChatMessageHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            self.running = True
            
            print(f"🚀 HTTP сервер запущен на http://{self.host}:{self.port}")
            print(f"📝 Сообщения сохраняются в: battlenet_messages.txt")
            print(f"📊 Статус: http://{self.host}:{self.port}/status")
            
        except Exception as e:
            print(f"❌ Ошибка запуска сервера: {e}")
            return False
        
        return True
    
    def stop(self):
        """Остановка сервера"""
        if self.server:
            self.server.shutdown()
            self.running = False
            print("🛑 Сервер остановлен")
    
    def is_running(self):
        """Проверка работы сервера"""
        return self.running


if __name__ == "__main__":
    server = MessageServer()
    
    try:
        if server.start():
            print("Нажмите Ctrl+C для остановки сервера")
            while server.is_running():
                time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Получен сигнал остановки")
    finally:
        server.stop()
