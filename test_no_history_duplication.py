#!/usr/bin/env python3
"""
Тестовый скрипт для проверки исправления дублирования сообщений из истории
"""

import os
import time
from pathlib import Path

def test_message_file_behavior():
    """Тестирует поведение файла сообщений"""
    
    print("=== Тест исправления дублирования сообщений из истории ===")
    print()
    
    messages_file = "battlenet_messages.txt"
    
    # Проверяем существование файла
    if os.path.exists(messages_file):
        # Получаем размер файла до запуска
        initial_size = os.path.getsize(messages_file)
        
        # Читаем последние строки
        with open(messages_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            initial_line_count = len(lines)
            
        print(f"📁 Файл сообщений: {messages_file}")
        print(f"📊 Размер файла: {initial_size} байт")
        print(f"📝 Количество строк: {initial_line_count}")
        print()
        
        if initial_line_count > 0:
            print("📋 Последние 3 сообщения в файле:")
            for i, line in enumerate(lines[-3:], 1):
                print(f"   {i}. {line.strip()}")
            print()
        
    else:
        print(f"📁 Файл {messages_file} не существует")
        initial_size = 0
        initial_line_count = 0
        print()
    
    print("✅ ЧТО ИСПРАВЛЕНО:")
    print("   1. JavaScript теперь помечает все существующие сообщения как обработанные")
    print("   2. При запуске игнорируются ВСЕ сообщения, которые уже есть на странице")
    print("   3. Обрабатываются только НОВЫЕ сообщения после запуска мониторинга")
    print()
    
    print("🔧 ИЗМЕНЕНИЯ В КОДЕ:")
    print("   - Добавлен флаг isInitialized")
    print("   - Добавлена функция markExistingMessagesAsProcessed()")
    print("   - Периодическое сканирование работает только после инициализации")
    print("   - Все существующие сообщения помечаются как уже обработанные")
    print()
    
    print("🚀 РЕЗУЛЬТАТ:")
    print("   ✅ При перезапуске battlenet_manual_injector.py старые сообщения НЕ дублируются")
    print("   ✅ В файл добавляются только новые сообщения после запуска")
    print("   ✅ Никаких повторных записей из истории чата")
    print()
    
    print("🧪 ДЛЯ ТЕСТИРОВАНИЯ:")
    print("   1. Запустите battlenet_manual_injector.py")
    print("   2. Убедитесь, что старые сообщения не добавляются в файл")
    print("   3. Отправьте новое сообщение в Battle.net")
    print("   4. Проверьте, что только новое сообщение добавилось в файл")
    print()
    
    return {
        'initial_size': initial_size,
        'initial_line_count': initial_line_count,
        'file_exists': os.path.exists(messages_file)
    }

if __name__ == "__main__":
    test_message_file_behavior()
