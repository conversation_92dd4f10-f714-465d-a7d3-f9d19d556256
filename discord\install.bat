@echo off
echo Установка зависимостей для Discord бота...
echo.

REM Проверяем наличие Python
py --version >nul 2>&1
if errorlevel 1 (
    echo Ошибка: Python не найден. Установите Python 3.8 или выше.
    echo Скачать можно с https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python найден:
py --version

echo.
echo Устанавливаем зависимости...
py -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo Ошибка при установке зависимостей!
    pause
    exit /b 1
)

echo.
echo Зависимости успешно установлены!
echo.
echo Следующие шаги:
echo 1. Скопируйте .env.example в .env
echo 2. Отредактируйте .env файл с вашими настройками
echo 3. Запустите бота командой: py main.py
echo.
pause
