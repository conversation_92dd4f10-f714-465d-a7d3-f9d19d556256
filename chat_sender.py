#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Интерактивный интерфейс для отправки сообщений в Battle.net чаты
"""

import requests
import json
import time
import os
from datetime import datetime

class ChatSender:
    def __init__(self):
        self.server_url = "http://localhost:8080"
        self.active_chats = {}
        
    def check_server_status(self):
        """Проверка статуса сервера"""
        try:
            response = requests.get(f"{self.server_url}/status", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def get_active_chats(self):
        """Получение списка активных чатов из логов"""
        chats = {}
        
        if not os.path.exists('battlenet_messages.txt'):
            return chats
        
        try:
            with open('battlenet_messages.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # Анализируем последние 100 строк для поиска активных чатов
            for line in lines[-100:]:
                if '[Chat:' in line:
                    try:
                        # Извлекаем chat_id из строки типа [Chat:403426477]
                        start = line.find('[Chat:') + 6
                        end = line.find(']', start)
                        chat_id = line[start:end]
                        
                        # Извлекаем имя пользователя если есть
                        if '] ' in line and ' (' in line:
                            user_part = line.split('] ', 1)[1]
                            if ' (' in user_part:
                                username = user_part.split(' (')[0]
                                chats[chat_id] = username
                            
                    except:
                        continue
                        
        except Exception as e:
            print(f"Ошибка чтения логов: {e}")
        
        return chats
    
    def send_message(self, chat_id, message):
        """Отправка сообщения"""
        data = {
            "chatId": chat_id,
            "message": message
        }
        
        try:
            response = requests.post(f"{self.server_url}/send-message", json=data)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('success', False), result.get('error', '')
            else:
                return False, f"HTTP {response.status_code}"
                
        except Exception as e:
            return False, str(e)
    
    def display_menu(self):
        """Отображение главного меню"""
        print("\n" + "=" * 60)
        print("🎮 Battle.net Chat Sender")
        print("=" * 60)
        
        if not self.check_server_status():
            print("❌ Сервер мониторинга не доступен!")
            print("   Убедитесь, что battlenet_manual_injector.py запущен")
            return False
        
        print("✅ Сервер мониторинга активен")
        
        # Получаем активные чаты
        self.active_chats = self.get_active_chats()
        
        if not self.active_chats:
            print("⚠️ Активные чаты не найдены")
            print("   Отправьте или получите сообщения в Battle.net для обнаружения чатов")
            return False
        
        print(f"\n📋 Найдено {len(self.active_chats)} активных чатов:")
        
        chat_list = list(self.active_chats.items())
        for i, (chat_id, username) in enumerate(chat_list, 1):
            print(f"   {i}. {username} (ID: {chat_id})")
        
        print("\n🔧 Команды:")
        print("   1-9: Выбрать чат для отправки сообщения")
        print("   r: Обновить список чатов")
        print("   q: Выход")
        
        return True
    
    def select_chat_and_send(self):
        """Выбор чата и отправка сообщения"""
        while True:
            if not self.display_menu():
                time.sleep(2)
                continue
            
            choice = input("\n👉 Выберите действие: ").strip().lower()
            
            if choice == 'q':
                print("👋 До свидания!")
                break
            elif choice == 'r':
                print("🔄 Обновление списка чатов...")
                continue
            elif choice.isdigit():
                chat_num = int(choice)
                chat_list = list(self.active_chats.items())
                
                if 1 <= chat_num <= len(chat_list):
                    chat_id, username = chat_list[chat_num - 1]
                    
                    print(f"\n💬 Отправка сообщения пользователю: {username}")
                    print("   (Введите пустую строку для отмены)")
                    
                    message = input("📝 Сообщение: ").strip()
                    
                    if message:
                        print(f"📤 Отправка сообщения...")
                        success, error = self.send_message(chat_id, message)
                        
                        if success:
                            print(f"✅ Сообщение отправлено пользователю {username}")
                            
                            # Логируем отправленное сообщение
                            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            with open('sent_messages.txt', 'a', encoding='utf-8') as f:
                                f.write(f"[{timestamp}] TO {username} (Chat:{chat_id}): {message}\n")
                                
                        else:
                            print(f"❌ Ошибка отправки: {error}")
                    else:
                        print("⏭️ Отправка отменена")
                else:
                    print("❌ Неверный номер чата")
            else:
                print("❌ Неверная команда")

def main():
    """Главная функция"""
    try:
        sender = ChatSender()
        sender.select_chat_and_send()
    except KeyboardInterrupt:
        print("\n👋 До свидания!")
    except Exception as e:
        print(f"💥 Ошибка: {e}")

if __name__ == "__main__":
    main()
