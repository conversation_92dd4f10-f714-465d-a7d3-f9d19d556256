# Исправление проблемы дублирования сообщений

## Проблема
При каждом перезапуске Discord бота все сообщения из истории заново отправлялись в каналы.

## Решение
Добавлен механизм отслеживания позиции в файле сообщений, чтобы избежать повторной обработки старых сообщений.

## Что изменилось

### 1. Новое поле в состоянии бота
- Добавлено поле `last_processed_position` в файл `discord_bot_state.json`
- Сохраняет позицию последнего обработанного сообщения в файле

### 2. Изменена логика запуска
- **РАНЬШЕ**: При запуске обрабатывались последние 50 сообщений из файла
- **СЕЙЧАС**: При запуске позиция устанавливается на конец файла, обрабатываются только НОВЫЕ сообщения

### 3. Новые команды Discord бота

#### `!reset_position`
- Сбрасывает позицию файла на конец
- Используйте, если нужно избежать обработки старых сообщений

#### `!reprocess <количество>`
- Принудительно обрабатывает последние N сообщений (1-200)
- Используйте для восстановления пропущенных сообщений
- **Внимание**: может привести к дублированию!

#### Существующие команды
- `!status` - статус бота
- `!channels` - список каналов пользователей  
- `!chatids` - сопоставления chat_id
- `!restore` - восстановление chat_id из файла
- `!mappings` - содержимое файла сопоставлений

## Как использовать

### Обычный запуск
```bash
# Запускаем battlenet_manual_injector.py
python battlenet_manual_injector.py

# В другом терминале запускаем Discord бота
cd discord
python main.py
```

### Если нужно восстановить пропущенные сообщения
1. Запустите бота
2. В Discord канале выполните: `!reprocess 50` (или другое количество)

### Если бот обрабатывает старые сообщения
1. В Discord канале выполните: `!reset_position`
2. Перезапустите бота

## Техническая информация

### Файлы изменены
- `discord/discord_bot.py` - основная логика бота
- `discord_bot_state.json` - добавлено поле `last_processed_position`

### Как работает
1. При запуске бот загружает сохраненную позицию из файла состояния
2. Если позиции нет, устанавливается на конец файла сообщений
3. Мониторинг файла начинается с этой позиции
4. Обрабатываются только новые строки, добавленные после этой позиции
5. Позиция периодически сохраняется в файл состояния

### Безопасность
- Старые сообщения больше не обрабатываются автоматически
- Состояние сохраняется каждые 10 обработанных сообщений
- Можно вручную управлять обработкой через команды

## Результат
✅ **Проблема решена!** Теперь можно перезапускать бота без дублирования сообщений.
