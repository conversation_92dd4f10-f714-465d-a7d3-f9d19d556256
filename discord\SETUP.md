# Настройка Discord Бота для WoW

## Шаг 1: Создание Discord приложения

1. Перейдите на https://discord.com/developers/applications
2. Нажмите "New Application"
3. Введите имя приложения (например, "WoW Message Bot")
4. Нажмите "Create"

## Шаг 2: Настройка бота

1. В левом меню выберите "Bot"
2. Нажмите "Add Bot"
3. В разделе "Token" нажмите "Copy" чтобы скопировать токен бота
4. **ВАЖНО**: Сохраните токен в безопасном месте, он понадобится позже

### Настройки бота:
- **Public Bot**: Отключите (чтобы только вы могли добавить бота)
- **Requires OAuth2 Code Grant**: Отключите
- **Privileged Gateway Intents**:
  - Message Content Intent: **Включите**
  - Server Members Intent: Можно оставить выключенным
  - Presence Intent: Можно оставить выключенным

## Шаг 3: Получение разрешений

1. В левом меню выберите "OAuth2" → "URL Generator"
2. В разделе "Scopes" выберите:
   - `bot`
3. В разделе "Bot Permissions" выберите:
   - `Manage Channels` (для создания каналов)
   - `Send Messages` (для отправки сообщений)
   - `Read Message History` (для чтения истории)
   - `Use Slash Commands` (опционально)

4. Скопируйте сгенерированную ссылку из поля "Generated URL"

## Шаг 4: Добавление бота на сервер

1. Откройте скопированную ссылку в браузере
2. Выберите сервер, на который хотите добавить бота
3. Подтвердите разрешения
4. Нажмите "Authorize"

## Шаг 5: Получение ID сервера

1. В Discord включите режим разработчика:
   - Настройки → Расширенные → Режим разработчика (включить)
2. Щелкните правой кнопкой мыши на название сервера
3. Выберите "Копировать ID сервера"
4. Сохраните этот ID

## Шаг 6: Установка зависимостей

```bash
# Перейдите в папку discord
cd discord

# Установите зависимости
pip install -r requirements.txt
```

## Шаг 7: Настройка конфигурации

1. Скопируйте файл конфигурации:
```bash
copy .env.example .env
```

2. Отредактируйте файл `.env`:
```env
# Токен бота (из шага 2)
DISCORD_TOKEN=ваш_токен_бота_здесь

# ID сервера (из шага 5)
GUILD_ID=ваш_id_сервера_здесь

# Путь к файлу сообщений (относительно папки discord)
BATTLENET_MESSAGES_PATH=../battlenet_messages.txt

# Название категории для каналов
CATEGORY_NAME=WoW Messages
```

## Шаг 8: Запуск бота

```bash
# Из папки discord
py main.py
```

Или из корневой папки:
```bash
py discord/main.py
```

## Проверка работы

1. Бот должен подключиться к серверу
2. Создастся категория "WoW Messages" (или другая, указанная в настройках)
3. При появлении новых сообщений в `battlenet_messages.txt` будут создаваться каналы для пользователей
4. Сообщения будут дублироваться в соответствующие каналы

## Команды бота

- `!status` - Показать статус бота
- `!channels` - Показать список каналов пользователей

## Устранение неполадок

### Бот не подключается
- Проверьте правильность токена в `.env`
- Убедитесь, что бот добавлен на сервер
- Проверьте правильность GUILD_ID

### Не создаются каналы
- Убедитесь, что у бота есть права "Manage Channels"
- Проверьте, что файл `battlenet_messages.txt` существует и доступен для чтения

### Сообщения не отправляются
- Убедитесь, что у бота есть права "Send Messages"
- Проверьте логи на наличие ошибок

### Файл не найден
- Убедитесь, что путь в `BATTLENET_MESSAGES_PATH` правильный
- Проверьте, что файл `battlenet_messages.txt` существует

## Логи

Бот выводит подробные логи в консоль:
- Информация о подключении
- Создание новых каналов
- Обработка сообщений
- Ошибки

## Остановка бота

Нажмите `Ctrl+C` в консоли для остановки бота.
