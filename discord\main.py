#!/usr/bin/env python3
"""
Главный файл для запуска Discord бота
"""

import asyncio
import os
import sys
from dotenv import load_dotenv
import traceback

from discord_bot import WoWDiscordBot

async def main():
    """Главная функция"""
    # Загружаем переменные окружения
    load_dotenv()
    
    # Получаем настройки из переменных окружения
    token = os.getenv('DISCORD_TOKEN')
    guild_id = os.getenv('GUILD_ID')
    category_name = os.getenv('CATEGORY_NAME', 'WoW Messages')
    battlenet_messages_path = os.getenv('BATTLENET_MESSAGES_PATH', '../battlenet_messages.txt')
    
    # Проверяем обязательные параметры
    if not token:
        print("Ошибка: DISCORD_TOKEN не установлен в .env файле")
        sys.exit(1)
    
    if not guild_id:
        print("Ошибка: GUILD_ID не установлен в .env файле")
        sys.exit(1)
    
    try:
        guild_id = int(guild_id)
    except ValueError:
        print("Ошибка: GUILD_ID должен быть числом")
        sys.exit(1)
    
    # Проверяем существование файла сообщений
    if not os.path.exists(battlenet_messages_path):
        print(f"Предупреждение: Файл сообщений не найден: {battlenet_messages_path}")
        print("Бот будет ждать появления файла...")
    
    # Создаем и запускаем бота
    bot = WoWDiscordBot(
        guild_id=guild_id,
        category_name=category_name,
        battlenet_messages_path=battlenet_messages_path
    )
    
    try:
        print("Запускаем Discord бота...")
        print(f"Сервер ID: {guild_id}")
        print(f"Категория: {category_name}")
        print(f"Файл сообщений: {battlenet_messages_path}")
        print("-" * 50)
        
        await bot.start(token)
    except KeyboardInterrupt:
        print("\nПолучен сигнал прерывания, останавливаем бота...")
    except Exception as e:
        print(f"Ошибка при запуске бота: {e}")
        traceback.print_exc()
    finally:
        if not bot.is_closed():
            await bot.close()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nПрограмма завершена пользователем")
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        sys.exit(1)
