import discord
from discord.ext import commands
import asyncio
import os
import aiohttp
from datetime import datetime
from typing import Dict, Optional, Set
import json

from message_parser import BattlenetMessageParser, ParsedMessage
from file_monitor import FileMonitor

class WoWDiscordBot(commands.Bot):
    """Discord бот для пересылки сообщений из WoW"""
    
    def __init__(self, guild_id: int, category_names: str, battlenet_messages_path: str):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True

        super().__init__(command_prefix='!', intents=intents)

        self.guild_id = guild_id
        # Парсим список категорий из строки (разделенные запятыми)
        self.category_names = [name.strip() for name in category_names.split(',')]
        self.battlenet_messages_path = battlenet_messages_path
        
        self.parser = BattlenetMessageParser()
        self.file_monitor = FileMonitor(battlenet_messages_path, self.process_new_message)
        
        # Кэш каналов: {username: channel_id}
        self.user_channels: Dict[str, int] = {}

        # Сопоставление пользователей с chat_id: {username: chat_id}
        self.user_chat_ids: Dict[str, str] = {}

        # Обратное сопоставление: {channel_id: username}
        self.channel_to_user: Dict[int, str] = {}

        # Кэш категорий: {category_name: CategoryChannel}
        self.categories: Dict[str, discord.CategoryChannel] = {}

        # Текущая активная категория для новых каналов
        self.current_category: Optional[discord.CategoryChannel] = None

        # Последние сообщения для группировки продолжений
        self.last_messages: Dict[str, ParsedMessage] = {}

        # Файл для сохранения состояния
        self.state_file = "discord_bot_state.json"

        # Файл для сопоставлений пользователей и chat_id
        self.chat_mappings_file = "chat_mappings.txt"



        # URL для отправки сообщений через HTTP API
        self.battlenet_api_url = "http://localhost:8080"

        # Словарь для конвертации эмодзи в текстовые смайлики
        self.emoji_to_text = {
            '🙂': ':)', '😊': ':)', '😀': ':D', '😃': ':D', '😄': ':D', '😁': ':D',
            '😆': 'xD', '😂': 'xD', '🤣': 'xD', '😉': ';)', '😘': ':*', '😍': '<3',
            '🥰': '<3', '😢': ':(', '😭': ':(', '😞': ':(', '☹️': ':(', '🙁': ':(',
            '😔': ':(', '😕': ':/', '😐': ':|', '😑': ':|', '😒': ':|', '🙄': ':|',
            '😏': ':>', '😎': 'B)', '🤔': ':?', '😮': ':o', '😯': ':o', '😲': ':O',
            '😱': ':O', '😨': ':O', '😰': ':S', '😅': ':)', '😬': ':S', '🤐': ':|',
            '🤨': ':|', '🧐': ':?', '😴': 'zzz', '🤤': ':P', '😋': ':P', '😛': ':P',
            '😜': ';P', '🤪': 'xP', '😝': 'xP', '🤑': '$)', '🤗': ':)', '🤭': ':)',
            '🤫': 'shh', '🤥': ':|', '😶': ':|', '😤': '>:(', '😠': '>:(', '😡': '>:(',
            '🤬': '>:(', '😈': '>:)', '👿': '>:(', '💀': 'x_x', '☠️': 'x_x', '💩': ':P',
            '🤡': ':o)', '👻': 'boo', '👽': 'o_O', '🤖': ':|', '😺': ':)', '😸': ':D',
            '😹': 'xD', '😻': '<3', '😼': ':>', '😽': ':*', '🙀': ':O', '😿': ':(',
            '😾': '>:(', '❤️': '<3', '💛': '<3', '💚': '<3', '💙': '<3', '💜': '<3',
            '🖤': '</3', '🤍': '<3', '🤎': '<3', '💔': '</3', '💕': '<3', '💖': '<3',
            '💗': '<3', '💘': '<3', '💝': '<3', '💞': '<3', '💟': '<3', '♥️': '<3',
            '💋': ':*', '👍': '+1', '👎': '-1', '👌': 'ok', '✌️': 'v', '🤞': 'x',
            '🤟': '<3', '🤘': 'm/', '🤙': 'call', '👋': 'hi', '🖐️': 'hi', '✋': 'stop',
            '🖖': 'v', '👏': '*clap*', '🙌': 'yay', '🤝': '*shake*', '🙏': 'pray',
            '✊': 'fist', '👊': 'punch', '🤛': 'punch', '🤜': 'punch', '💪': 'strong',
            '🦾': 'strong', '🔥': 'fire', '💯': '100', '💢': '!!', '💥': 'boom',
            '💫': '*', '💦': '~', '💨': 'fast', '🎉': 'party', '🎊': 'party',
            '🎈': 'party', '🎁': 'gift', '🏆': 'win', '🥇': '1st', '🥈': '2nd',
            '🥉': '3rd', '⭐': '*', '🌟': '*', '✨': '*', '⚡': 'zap', '☀️': 'sun',
            '🌙': 'moon', '🌈': 'rainbow', '☔': 'rain', '❄️': 'snow', '⛄': 'snow',
            '🌊': 'wave', '🔴': 'red', '🟠': 'orange', '🟡': 'yellow', '🟢': 'green',
            '🔵': 'blue', '🟣': 'purple', '⚫': 'black', '⚪': 'white', '🟤': 'brown',
            '🔶': 'diamond', '🔷': 'diamond', '🔸': 'diamond', '🔹': 'diamond',
            '💎': 'gem', '💰': 'money', '💵': 'money', '💴': 'money', '💶': 'money',
            '💷': 'money', '💸': 'money', '💳': 'card', '🎮': 'game', '🕹️': 'game',
            '🎯': 'target', '🎲': 'dice', '🃏': 'card', '🎪': 'circus', '🎭': 'mask',
            '🎨': 'art', '🎬': 'movie', '🎤': 'mic', '🎧': 'music', '🎵': 'music',
            '🎶': 'music', '🎼': 'music', '🥁': 'drums', '🎸': 'guitar', '🎹': 'piano',
            '🎺': 'trumpet', '🎻': 'violin', '🪕': 'banjo', '🎷': 'sax', '🪗': 'accordion',
            '🪘': 'drum', '🔔': 'bell', '🔕': 'mute', '📢': 'loud', '📣': 'loud',
            '📯': 'horn', '🔊': 'loud', '🔉': 'sound', '🔈': 'sound', '🔇': 'mute',
            '📱': 'phone', '📞': 'phone', '☎️': 'phone', '📟': 'beep', '📠': 'fax',
            '📺': 'tv', '📻': 'radio', '🎙️': 'mic', '🎚️': 'sound', '🎛️': 'control',
            '🧭': 'compass', '⏰': 'alarm', '⏲️': 'timer', '⏱️': 'timer', '⏳': 'time',
            '⌛': 'time', '📅': 'date', '📆': 'date', '🗓️': 'date', '📋': 'list',
            '📌': 'pin', '📍': 'pin', '📎': 'clip', '🖇️': 'clip', '📏': 'ruler',
            '📐': 'ruler', '✂️': 'cut', '🗃️': 'file', '🗄️': 'file', '🗑️': 'trash',
            '🔒': 'lock', '🔓': 'unlock', '🔏': 'lock', '🔐': 'lock', '🔑': 'key',
            '🗝️': 'key', '🔨': 'hammer', '🪓': 'axe', '⛏️': 'pick', '🔧': 'tool',
            '🔩': 'bolt', '⚙️': 'gear', '🧰': 'tools', '🧲': 'magnet', '⚖️': 'scale',
            '🦯': 'cane', '🔗': 'link', '⛓️': 'chain', '🧿': 'eye', '📿': 'beads',
            '💊': 'pill', '🧬': 'dna', '🦠': 'virus', '💉': 'shot', '🩸': 'blood',
            '🩹': 'bandage', '🩺': 'doctor', '🔬': 'science', '🔭': 'telescope',
            '📡': 'satellite', '💻': 'pc', '🖥️': 'pc', '🖨️': 'print', '⌨️': 'keyboard',
            '🖱️': 'mouse', '🖲️': 'trackball', '💽': 'disk', '💾': 'disk', '💿': 'cd',
            '📀': 'dvd', '🧮': 'calc', '🎥': 'camera', '📹': 'camera', '📷': 'camera',
            '📸': 'camera', '🔍': 'search', '🔎': 'search', '🕯️': 'candle', '💡': 'idea',
            '🔦': 'light', '🏮': 'lantern', '🪔': 'lamp', '📔': 'book', '📕': 'book',
            '📖': 'book', '📗': 'book', '📘': 'book', '📙': 'book', '📚': 'books',
            '📓': 'book', '📒': 'book', '📃': 'page', '📜': 'scroll', '📄': 'page',
            '📰': 'news', '🗞️': 'news', '📑': 'page', '🔖': 'bookmark', '🏷️': 'tag',
            '🪙': 'coin', '🧾': 'receipt', '💹': 'chart', '✉️': 'mail', '📧': 'email',
            '📨': 'mail', '📩': 'mail', '📤': 'outbox', '📥': 'inbox', '📦': 'box',
            '📫': 'mailbox', '📪': 'mailbox', '📬': 'mailbox', '📭': 'mailbox',
            '📮': 'mailbox', '🗳️': 'vote', '✏️': 'pencil', '✒️': 'pen', '🖋️': 'pen',
            '🖊️': 'pen', '🖌️': 'brush', '🖍️': 'crayon', '📝': 'memo', '💼': 'case',
            '📁': 'folder', '📂': 'folder', '🗂️': 'folder', '🗒️': 'note', '📇': 'card',
            '📈': 'up', '📉': 'down', '📊': 'chart', '📍': 'location'
        }

        # Добавляем команды
        self.add_commands()

    def convert_emoji_to_text(self, message: str) -> str:
        """Конвертирует эмодзи в текстовые смайлики для Battle.net"""
        result = message
        for emoji, text in self.emoji_to_text.items():
            result = result.replace(emoji, text)
        return result

    async def setup_hook(self):
        """Инициализация бота"""
        print(f"Бот {self.user} готов к работе!")
        
        # Загружаем состояние
        await self.load_state()

        # Загружаем сопоставления пользователей и chat_id
        await self.load_chat_mappings()

        # Запускаем мониторинг файла
        asyncio.create_task(self.file_monitor.start_monitoring())

        # Восстанавливаем chat_id для существующих каналов (если нужно)
        await self.restore_chat_ids_from_file()

        # Устанавливаем позицию файла на конец, чтобы игнорировать существующие сообщения
        await self.set_file_position_to_end()
    
    async def on_ready(self):
        """Вызывается когда бот готов"""
        guild = self.get_guild(self.guild_id)
        if not guild:
            print(f"Не удалось найти сервер с ID {self.guild_id}")
            return

        print(f"Подключен к серверу: {guild.name}")

        # Находим или создаем категории
        await self.initialize_categories(guild)

    async def on_message(self, message):
        """Обрабатывает сообщения в Discord"""
        # Игнорируем сообщения от бота
        if message.author == self.user:
            return

        # Обрабатываем команды
        await self.process_commands(message)

        # Проверяем, что сообщение в одной из наших категорий
        if not message.channel.category or message.channel.category not in self.categories.values():
            return

        # Находим пользователя по каналу
        username = self.channel_to_user.get(message.channel.id)

        if not username:
            # Попробуем найти через старый способ для совместимости
            for user, channel_id in self.user_channels.items():
                if channel_id == message.channel.id:
                    username = user
                    self.channel_to_user[message.channel.id] = username  # Обновляем кэш
                    break

        if not username:
            await message.channel.send("❌ Не удалось определить пользователя для этого канала")
            return

        # Получаем chat_id для пользователя
        chat_id = self.user_chat_ids.get(username)
        if not chat_id:
            await message.channel.send(f"❌ Не найден chat_id для пользователя {username}")
            return

        # Конвертируем эмодзи в текстовые смайлики
        content_with_text_emoji = self.convert_emoji_to_text(message.content)

        # Формируем сообщение с именем получателя: {товчикус-гордунни} 123
        formatted_message = f"{{{username}}} {content_with_text_emoji}"

        # Отправляем сообщение через HTTP API
        success = await self.send_battlenet_message(chat_id, formatted_message)

        if success:
            # Добавляем реакцию для подтверждения
            await message.add_reaction("✅")
        else:
            await message.add_reaction("❌")
            await message.channel.send("❌ Не удалось отправить сообщение")
        
    async def initialize_categories(self, guild: discord.Guild):
        """Инициализирует все категории"""
        for category_name in self.category_names:
            category = await self.get_or_create_category(guild, category_name)
            self.categories[category_name] = category

            # Первая категория становится текущей
            if not self.current_category:
                self.current_category = category

        print(f"Инициализированы категории: {list(self.categories.keys())}")

    async def get_or_create_category(self, guild: discord.Guild, category_name: str) -> discord.CategoryChannel:
        """Находит или создает конкретную категорию"""
        # Ищем существующую категорию
        for category in guild.categories:
            if category.name == category_name:
                return category

        # Создаем новую категорию
        category = await guild.create_category(category_name)
        print(f"Создана категория: {category_name}")
        return category

    def get_available_category(self) -> Optional[discord.CategoryChannel]:
        """Возвращает категорию с доступным местом для нового канала"""
        for category_name, category in self.categories.items():
            # Пропускаем категорию "booked" - в ней не создаём каналы
            if category_name.lower() == 'booked':
                continue

            # Discord лимит: 50 каналов в категории
            if len(category.channels) < 50:
                return category

        # Если все категории заполнены, возвращаем первую (кроме booked)
        for category_name, category in self.categories.items():
            if category_name.lower() != 'booked':
                return category

        return self.current_category
    
    async def get_or_create_user_channel(self, username: str) -> discord.TextChannel:
        """Находит или создает канал для пользователя"""
        channel_name = self.parser.get_channel_name(ParsedMessage(
            timestamp=datetime.now(),
            chat_id="",
            username=username,
            time_in_chat="",
            message="",
            original_sender=username if username != username.lower() else None
        ))
        
        # Проверяем кэш
        if username in self.user_channels:
            channel = self.get_channel(self.user_channels[username])
            if channel:
                return channel
            else:
                # Канал был удален, убираем из кэша
                del self.user_channels[username]
        
        guild = self.get_guild(self.guild_id)
        if not guild or not self.categories:
            raise Exception("Сервер или категории не найдены")

        # Ищем существующий канал во всех категориях
        for category in self.categories.values():
            for channel in category.channels:
                if isinstance(channel, discord.TextChannel) and channel.name == channel_name:
                    self.user_channels[username] = channel.id
                    await self.save_state()
                    return channel

        # Находим категорию с доступным местом
        target_category = self.get_available_category()
        if not target_category:
            raise Exception("Не удалось найти категорию с доступным местом")

        # Создаем новый канал
        channel = await guild.create_text_channel(
            name=channel_name,
            category=target_category,
            topic=f"Сообщения от пользователя: {username}"
        )

        print(f"Создан канал для пользователя {username} в категории {target_category.name}: #{channel_name}")
        
        self.user_channels[username] = channel.id

        # Обновляем обратное сопоставление
        self.channel_to_user[channel.id] = username

        await self.save_state()
        return channel
    
    async def process_new_message(self, line: str):
        """Обрабатывает новое сообщение из файла"""
        parsed = self.parser.parse_message(line)
        if not parsed:
            return

        try:
            await self.send_message_to_discord(parsed)
        except Exception as e:
            print(f"Ошибка при отправке сообщения в Discord: {e}")
    
    async def send_message_to_discord(self, parsed_msg: ParsedMessage):
        """Отправляет сообщение в соответствующий Discord канал"""
        # Определяем имя пользователя для канала
        if parsed_msg.is_from_message and parsed_msg.original_sender:
            channel_username = parsed_msg.original_sender
        elif parsed_msg.is_continuation:
            # Для продолжений используем последнее сообщение
            chat_key = f"{parsed_msg.chat_id}"
            if chat_key in self.last_messages:
                last_msg = self.last_messages[chat_key]
                if last_msg.is_from_message and last_msg.original_sender:
                    channel_username = last_msg.original_sender
                else:
                    channel_username = last_msg.username
            else:
                return  # Не знаем к какому пользователю относится продолжение
        else:
            channel_username = parsed_msg.username

        # Сохраняем сопоставление пользователя с chat_id
        if channel_username not in self.user_chat_ids or self.user_chat_ids[channel_username] != parsed_msg.chat_id:
            self.user_chat_ids[channel_username] = parsed_msg.chat_id
            await self.save_chat_mapping(channel_username, parsed_msg.chat_id)

        # Получаем или создаем канал
        channel = await self.get_or_create_user_channel(channel_username)

        # Обновляем обратное сопоставление
        self.channel_to_user[channel.id] = channel_username

        # Формируем сообщение в новом формате
        if parsed_msg.is_from_message and parsed_msg.original_sender:
            # Для FROM сообщений: [19:30:37] Товчикус-Гордунни: фыва
            time_str = parsed_msg.time_in_chat if parsed_msg.time_in_chat else "??:??:??"
            message_text = f"[{time_str}] **{parsed_msg.original_sender}**: {parsed_msg.message}"
        elif parsed_msg.is_continuation and parsed_msg.is_from_message:
            # Для продолжений FROM сообщений
            time_str = parsed_msg.time_in_chat if parsed_msg.time_in_chat else "??:??:??"
            if parsed_msg.original_sender:
                message_text = f"[{time_str}] **{parsed_msg.original_sender}**: {parsed_msg.message}"
            else:
                message_text = parsed_msg.message
        else:
            # Для остальных сообщений (старый формат для совместимости)
            display_username = self.parser.get_display_username(parsed_msg)
            time_str = parsed_msg.time_in_chat if parsed_msg.time_in_chat else "??:??"
            message_text = f"**{display_username}** ({time_str}): {parsed_msg.message}"

        # Отправляем сообщение
        await channel.send(message_text)

        # Сохраняем последнее сообщение для группировки продолжений
        if not parsed_msg.is_continuation:
            chat_key = f"{parsed_msg.chat_id}"
            self.last_messages[chat_key] = parsed_msg
    
    async def set_file_position_to_end(self):
        """Устанавливает позицию файла на конец, игнорируя все существующие сообщения"""
        if os.path.exists(self.battlenet_messages_path):
            try:
                file_size = os.path.getsize(self.battlenet_messages_path)
                self.file_monitor.last_position = file_size
                self.file_monitor.last_size = file_size
                print(f"Позиция файла установлена на конец: {file_size} байт")
                print("Все существующие сообщения будут проигнорированы")
            except Exception as e:
                print(f"Ошибка при установке позиции файла: {e}")
        else:
            print("Файл сообщений не найден, начинаем с позиции 0")
            self.file_monitor.last_position = 0
            self.file_monitor.last_size = 0

    async def process_existing_messages(self, limit: int = 50):
        """Обрабатывает существующие сообщения из файла (УСТАРЕЛО - используется только для восстановления)"""
        print("Обрабатываем существующие сообщения...")

        existing_messages = await self.file_monitor.read_existing_messages(limit)

        for line in existing_messages:
            parsed = self.parser.parse_message(line)
            if parsed:
                try:
                    await self.send_message_to_discord(parsed)
                except Exception as e:
                    print(f"Ошибка при обработке существующего сообщения: {e}")

        print(f"Обработано {len(existing_messages)} существующих сообщений")

    async def restore_chat_ids_from_file(self) -> int:
        """Восстанавливает chat_id для существующих каналов из файла сообщений"""
        restored_count = 0

        try:
            # Читаем последние 200 сообщений из файла
            existing_messages = await self.file_monitor.read_existing_messages(200)

            for line in existing_messages:
                parsed = self.parser.parse_message(line)
                if not parsed or not parsed.is_from_message:
                    continue

                username = parsed.original_sender
                if not username:
                    continue

                # Проверяем, есть ли канал для этого пользователя
                if username in self.user_channels:
                    # Если chat_id еще не сохранен, сохраняем его
                    if username not in self.user_chat_ids:
                        self.user_chat_ids[username] = parsed.chat_id
                        restored_count += 1
                        print(f"Восстановлен chat_id для {username}: {parsed.chat_id}")

            if restored_count > 0:
                await self.save_state()
                print(f"Восстановлено {restored_count} сопоставлений chat_id")

        except Exception as e:
            print(f"Ошибка при восстановлении chat_id: {e}")

        return restored_count

    async def load_chat_mappings(self):
        """Загружает сопоставления пользователей и chat_id из файла"""
        if not os.path.exists(self.chat_mappings_file):
            print("Файл сопоставлений не найден, создаем новый")
            return

        try:
            with open(self.chat_mappings_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            loaded_count = 0
            for line in lines:
                line = line.strip()
                if not line or ':' not in line:
                    continue

                try:
                    username, chat_id = line.split(':', 1)
                    username = username.strip()
                    chat_id = chat_id.strip()

                    if username and chat_id:
                        self.user_chat_ids[username] = chat_id
                        loaded_count += 1
                except ValueError:
                    print(f"Неверный формат строки в файле сопоставлений: {line}")
                    continue

            print(f"Загружено {loaded_count} сопоставлений из {self.chat_mappings_file}")

        except Exception as e:
            print(f"Ошибка при загрузке сопоставлений: {e}")

    async def save_chat_mapping(self, username: str, chat_id: str):
        """Сохраняет новое сопоставление пользователя и chat_id в файл"""
        try:
            # Проверяем, есть ли уже такая запись
            existing_mappings = {}
            if os.path.exists(self.chat_mappings_file):
                with open(self.chat_mappings_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if ':' in line:
                            existing_username, existing_chat_id = line.split(':', 1)
                            existing_mappings[existing_username.strip()] = existing_chat_id.strip()

            # Добавляем или обновляем запись
            existing_mappings[username] = chat_id

            # Записываем все сопоставления обратно в файл
            with open(self.chat_mappings_file, 'w', encoding='utf-8') as f:
                for user, cid in sorted(existing_mappings.items()):
                    f.write(f"{user}:{cid}\n")

            print(f"Сохранено сопоставление: {username} → {chat_id}")

        except Exception as e:
            print(f"Ошибка при сохранении сопоставления: {e}")

    async def remove_chat_mapping(self, username: str):
        """Удаляет сопоставление пользователя из файла"""
        try:
            if not os.path.exists(self.chat_mappings_file):
                return

            # Читаем все существующие сопоставления
            existing_mappings = {}
            with open(self.chat_mappings_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if ':' in line:
                        existing_username, existing_chat_id = line.split(':', 1)
                        existing_username = existing_username.strip()
                        if existing_username != username:  # Исключаем удаляемого пользователя
                            existing_mappings[existing_username] = existing_chat_id.strip()

            # Записываем обновленные сопоставления
            with open(self.chat_mappings_file, 'w', encoding='utf-8') as f:
                for user, cid in sorted(existing_mappings.items()):
                    f.write(f"{user}:{cid}\n")

            print(f"Удалено сопоставление для пользователя: {username}")

        except Exception as e:
            print(f"Ошибка при удалении сопоставления: {e}")

    async def send_battlenet_message(self, chat_id: str, message: str) -> bool:
        """Отправляет сообщение в Battle.net через HTTP API"""
        try:
            data = {
                "chatId": chat_id,
                "message": message
            }

            print(f"📤 Отправка в Battle.net (chat_id: {chat_id}): {message}")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.battlenet_api_url}/send-message",
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:

                    if response.status == 200:
                        result = await response.json()
                        if result.get('success'):
                            print(f"   ✅ Сообщение успешно отправлено")

                            # Логируем отправленное сообщение
                            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            try:
                                with open('../sent_messages.txt', 'a', encoding='utf-8') as f:
                                    f.write(f"[{timestamp}] TO Discord->BattleNet (Chat:{chat_id}): {message}\n")
                            except:
                                pass  # Не критично если не удалось записать лог

                            return True
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            print(f"   ❌ Ошибка API: {error_msg}")
                            return False
                    else:
                        print(f"   ❌ HTTP ошибка: {response.status}")
                        return False

        except aiohttp.ClientConnectorError:
            print(f"   ❌ Не удалось подключиться к серверу {self.battlenet_api_url}")
            print(f"   Убедитесь, что battlenet_manual_injector.py запущен")
            return False
        except asyncio.TimeoutError:
            print(f"   ❌ Таймаут при отправке сообщения")
            return False
        except Exception as e:
            print(f"   ❌ Ошибка при отправке: {e}")
            return False
    
    async def load_state(self):
        """Загружает состояние бота из файла"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    self.user_channels = state.get('user_channels', {})
                    self.user_chat_ids = state.get('user_chat_ids', {})

                    # Восстанавливаем обратное сопоставление
                    self.channel_to_user = {}
                    for username, channel_id in self.user_channels.items():
                        self.channel_to_user[channel_id] = username

                print(f"Загружено состояние: {len(self.user_channels)} каналов, {len(self.user_chat_ids)} chat_id")
            except Exception as e:
                print(f"Ошибка при загрузке состояния: {e}")

    async def save_state(self):
        """Сохраняет состояние бота в файл"""
        try:
            state = {
                'user_channels': self.user_channels,
                'user_chat_ids': self.user_chat_ids
            }
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Ошибка при сохранении состояния: {e}")
    
    async def close(self):
        """Закрытие бота"""
        self.file_monitor.stop_monitoring()
        await self.save_state()
        await super().close()

    def add_commands(self):
        """Добавляет команды к боту"""

        @self.command(name='status')
        async def status_command(ctx):
            """Показывает статус бота"""
            embed = discord.Embed(title="Статус WoW Discord Bot", color=0x00ff00)
            embed.add_field(name="Активных каналов", value=len(self.user_channels), inline=True)
            embed.add_field(name="Сопоставлений chat_id", value=len(self.user_chat_ids), inline=True)
            embed.add_field(name="Файл сообщений", value=self.battlenet_messages_path, inline=False)
            embed.add_field(name="Мониторинг", value="Активен" if self.file_monitor.is_running else "Остановлен", inline=True)

            # Добавляем информацию о категориях
            if self.categories:
                category_info = []
                for name, category in self.categories.items():
                    channel_count = len(category.channels)

                    if name.lower() == 'booked':
                        # Специальный статус для категории booked
                        status = "📋"
                        category_info.append(f"{status} {name}: {channel_count} (только чтение)")
                    else:
                        status = "🟢" if channel_count < 50 else "🔴"
                        category_info.append(f"{status} {name}: {channel_count}/50")

                embed.add_field(
                    name="Категории",
                    value="\n".join(category_info) if category_info else "Нет категорий",
                    inline=False
                )

            await ctx.send(embed=embed)

        @self.command(name='channels')
        async def channels_command(ctx):
            """Показывает список каналов пользователей"""
            if not self.user_channels:
                await ctx.send("Нет активных каналов пользователей")
                return

            channel_list = []
            for username, channel_id in self.user_channels.items():
                channel = self.get_channel(channel_id)
                if channel:
                    channel_list.append(f"• {username} → #{channel.name}")
                else:
                    channel_list.append(f"• {username} → (канал удален)")

            embed = discord.Embed(title="Каналы пользователей", color=0x0099ff)
            embed.description = "\n".join(channel_list)

            await ctx.send(embed=embed)

        @self.command(name='chatids')
        async def chatids_command(ctx):
            """Показывает сопоставления пользователей с chat_id"""
            if not self.user_chat_ids:
                await ctx.send("Нет сопоставлений пользователей с chat_id")
                return

            chatid_list = []
            for username, chat_id in self.user_chat_ids.items():
                chatid_list.append(f"• {username} → Chat:{chat_id}")

            embed = discord.Embed(title="Сопоставления Chat ID", color=0xff9900)
            embed.description = "\n".join(chatid_list)
            embed.add_field(
                name="Информация",
                value="Эти chat_id используются для отправки сообщений в Battle.net",
                inline=False
            )

            await ctx.send(embed=embed)

        @self.command(name='restore')
        async def restore_command(ctx):
            """Восстанавливает chat_id для существующих каналов из файла сообщений"""
            restored_count = await self.restore_chat_ids_from_file()

            embed = discord.Embed(title="Восстановление Chat ID", color=0x00ff00)
            embed.add_field(name="Результат", value=f"Восстановлено {restored_count} сопоставлений", inline=False)
            embed.add_field(name="Всего каналов", value=len(self.user_channels), inline=True)
            embed.add_field(name="Всего chat_id", value=len(self.user_chat_ids), inline=True)

            await ctx.send(embed=embed)

        @self.command(name='mappings')
        async def mappings_command(ctx):
            """Показывает содержимое файла сопоставлений"""
            try:
                if not os.path.exists(self.chat_mappings_file):
                    await ctx.send("Файл сопоставлений не найден")
                    return

                with open(self.chat_mappings_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if not content:
                    await ctx.send("Файл сопоставлений пуст")
                    return

                embed = discord.Embed(title="Файл сопоставлений (chat_mappings.txt)", color=0x9932cc)

                # Разбиваем на строки и форматируем
                lines = content.split('\n')
                formatted_lines = []
                for line in lines:
                    if ':' in line:
                        username, chat_id = line.split(':', 1)
                        formatted_lines.append(f"• {username.strip()} → Chat:{chat_id.strip()}")

                embed.description = "\n".join(formatted_lines) if formatted_lines else "Нет корректных записей"
                embed.add_field(name="Файл", value=self.chat_mappings_file, inline=False)

                await ctx.send(embed=embed)

            except Exception as e:
                await ctx.send(f"Ошибка при чтении файла: {e}")

        @self.command(name='reprocess')
        async def reprocess_command(ctx, limit: int = 50):
            """Принудительно обрабатывает последние N сообщений из файла"""
            if limit < 1 or limit > 200:
                await ctx.send("❌ Лимит должен быть от 1 до 200")
                return

            await ctx.send(f"🔄 Начинаем обработку последних {limit} сообщений...")

            try:
                await self.process_existing_messages(limit)

                embed = discord.Embed(title="Переобработка сообщений", color=0x00ff00)
                embed.add_field(name="Результат", value=f"Обработано последних {limit} сообщений", inline=False)
                embed.add_field(name="Предупреждение", value="Это может привести к дублированию сообщений", inline=False)

                await ctx.send(embed=embed)

            except Exception as e:
                await ctx.send(f"❌ Ошибка при переобработке: {e}")

        @self.command(name='ignore_old')
        async def ignore_old_command(ctx):
            """Игнорирует все текущие сообщения в файле, обрабатывает только новые"""
            try:
                await self.set_file_position_to_end()

                embed = discord.Embed(title="Игнорирование старых сообщений", color=0x00ff00)
                embed.add_field(name="Результат", value="Позиция установлена на конец файла", inline=False)
                embed.add_field(name="Эффект", value="Все текущие сообщения проигнорированы, будут обрабатываться только новые", inline=False)

                await ctx.send(embed=embed)

            except Exception as e:
                await ctx.send(f"❌ Ошибка при установке позиции: {e}")

        @self.command(name='emoji_test')
        async def emoji_test_command(ctx, *, text: str = None):
            """Тестирует конвертацию эмодзи в текстовые смайлики"""
            if not text:
                # Показываем примеры конвертации
                examples = [
                    "🙂 → :)", "😀 → :D", "😂 → xD", "😉 → ;)", "😍 → <3",
                    "😢 → :(", "😎 → B)", "🤔 → :?", "👍 → +1", "👎 → -1",
                    "🔥 → fire", "💯 → 100", "🎉 → party", "❤️ → <3", "💔 → </3"
                ]

                embed = discord.Embed(title="Конвертация эмодзи в текст", color=0x00ff00)
                embed.add_field(
                    name="Примеры конвертации",
                    value="\n".join(examples[:10]),
                    inline=True
                )
                embed.add_field(
                    name="Ещё примеры",
                    value="\n".join(examples[10:]),
                    inline=True
                )
                embed.add_field(
                    name="Использование",
                    value="!emoji_test ваш текст с эмодзи 🙂😀🔥",
                    inline=False
                )

                await ctx.send(embed=embed)
            else:
                # Конвертируем переданный текст
                converted = self.convert_emoji_to_text(text)

                embed = discord.Embed(title="Результат конвертации", color=0x0099ff)
                embed.add_field(name="Исходный текст", value=text, inline=False)
                embed.add_field(name="Конвертированный", value=converted, inline=False)
                embed.add_field(
                    name="Информация",
                    value="Так будет выглядеть ваше сообщение в Battle.net",
                    inline=False
                )

                await ctx.send(embed=embed)
