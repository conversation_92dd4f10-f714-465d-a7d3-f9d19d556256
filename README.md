# Battle.net Chat Injector

Система для захвата сообщений из чата Battle.net с использованием Chrome DevTools Protocol и JavaScript injection.

## 🎯 Описание

Этот инструмент внедряет JavaScript код в процесс Battle.net для мониторинга сообщений чата в реальном времени и сохранения их в текстовый файл.

## 📋 Требования

### Системные требования:
- Windows 10/11
- Battle.net клиент
- Python 3.7+

### Python зависимости:
```bash
py -m pip install websockets aiohttp psutil
```

## 📁 Файлы проекта

- `battlenet_chat_injector.py` - Главный скрипт
- `battlenet_cdp_client.py` - CDP клиент для подключения к Battle.net
- `battlenet_message_server.py` - HTTP сервер для приема сообщений
- `chat_monitor.js` - JavaScript код для мониторинга чата
- `README.md` - Данная инструкция

## 🚀 Установка и запуск

### 1. Установка зависимостей
```bash
py -m pip install websockets aiohttp psutil
```

### 2. Подготовка Battle.net
Скрипт автоматически перезапустит Battle.net с необходимыми параметрами debug.

### 3. Запуск
```bash
py battlenet_chat_injector.py
```

## 🔧 Как это работает

### Этап 1: Подготовка
1. Скрипт закрывает все процессы Battle.net
2. Запускает Battle.net с флагом `--remote-debugging-port=9222`
3. Запускает HTTP сервер на порту 8080

### Этап 2: Подключение
1. Подключается к Battle.net через Chrome DevTools Protocol
2. Находит подходящую вкладку/фрейм с чатом
3. Устанавливает WebSocket соединение

### Этап 3: Внедрение
1. Внедряет JavaScript код мониторинга в страницу Battle.net
2. Настраивает MutationObserver для отслеживания изменений DOM
3. Запускает периодическое сканирование сообщений

### Этап 4: Мониторинг
1. JavaScript код отслеживает новые сообщения в чате
2. Фильтрует системные сообщения и дубликаты
3. Отправляет сообщения на HTTP сервер
4. Сервер сохраняет сообщения в файл `battlenet_messages.txt`

## 📝 Результат

Все сообщения сохраняются в файл `battlenet_messages.txt` в формате:
```
[2024-01-15 14:30:25] Пример сообщения из чата
[2024-01-15 14:30:30] Другое сообщение
```

## 🔍 Мониторинг работы

- **Статус сервера**: http://localhost:8080/status
- **Консольный вывод**: Показывает все этапы работы и ошибки
- **Файл сообщений**: `battlenet_messages.txt` обновляется в реальном времени

## ⚠️ Возможные проблемы

### Battle.net не запускается с debug портом
- Убедитесь, что Battle.net полностью закрыт
- Проверьте права администратора
- Попробуйте другой порт (измените в коде)

### Не находится вкладка чата
- Откройте чат в Battle.net вручную
- Убедитесь, что вы авторизованы
- Проверьте консольный вывод для диагностики

### Сообщения не сохраняются
- Проверьте статус HTTP сервера
- Убедитесь, что порт 8080 свободен
- Проверьте права на запись в папку

### JavaScript не внедряется
- Проверьте подключение к CDP
- Убедитесь, что файл `chat_monitor.js` существует
- Проверьте консоль браузера в Battle.net (F12)

## 🛠️ Настройка

### Изменение порта HTTP сервера
В файле `battlenet_message_server.py`:
```python
server = MessageServer(port=8081)  # Изменить порт
```

### Изменение порта CDP
В файле `battlenet_cdp_client.py`:
```python
client = BattleNetCDPClient(debug_port=9223)  # Изменить порт
```

### Настройка фильтров сообщений
В файле `chat_monitor.js` в функции `isValidMessage()` можно изменить логику фильтрации.

## 🔄 Остановка

Нажмите `Ctrl+C` для корректной остановки всех компонентов.

## 📊 Диагностика

### Проверка подключения к CDP
```bash
curl http://localhost:9222/json
```

### Проверка HTTP сервера
```bash
curl http://localhost:8080/status
```

### Просмотр логов Battle.net
Логи сохраняются в папке Battle.net, проверьте на наличие ошибок.

## 🎮 Совместимость

Протестировано с:
- Battle.net версии 1.x
- Windows 10/11
- Python 3.8-3.11

## 📞 Поддержка

При возникновении проблем:
1. Проверьте консольный вывод на наличие ошибок
2. Убедитесь, что все зависимости установлены
3. Проверьте, что Battle.net запущен и авторизован
4. Попробуйте перезапустить скрипт

## ⚖️ Ограничения

- Работает только с Battle.net на Windows
- Требует перезапуска Battle.net с debug параметрами
- Может не работать с некоторыми версиями Battle.net
- Зависит от структуры DOM интерфейса Battle.net
