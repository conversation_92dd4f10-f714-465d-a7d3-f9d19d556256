#!/usr/bin/env python3
"""
Тестовый скрипт для проверки парсера сообщений
"""

from message_parser import BattlenetMessageParser

def test_parser():
    """Тестирует парсер на примерах сообщений"""
    parser = BattlenetMessageParser()
    
    # Тестовые сообщения
    test_messages = [
        # Реальный формат FROM сообщений с фигурными скобками
        "[2025-07-15 15:40:26] [Chat:403426477] [продолжение]: [19:40:25] {FROM Товчикус-Гордунни}: алооооооооо",
        "[2025-07-15 15:39:03] [Chat:403426477] [продолжение]: [19:39:01] {FROM Товчикус-Гордунни}: ф",
        "[2025-07-15 15:30:38] [Chat:1126056253] [продолжение]: [20:15:22] {FROM TestUser}: тестовое сообщение",

        # Старый формат для совместимости
        "[2025-07-15 14:30:25] [Chat:403426477] towner (2:26 PM): [FROM: TestSender] Сообщение от TestSender",
        "[2025-07-15 14:30:25] [Chat:403426477] [продолжение]: [FROM: TestSender] Продолжение от TestSender",

        # Обычные сообщения (должны игнорироваться)
        "[2025-07-15 14:30:25] [Chat:403426477] towner (2:26 PM): Обычное сообщение",
        "[2025-07-15 14:30:25] [Chat:403426477] [продолжение]: Продолжение сообщения",
        "[2025-07-15 14:30:25] [Chat:1126056253] SpeedyRogue (6:47 PM): 123",
    ]
    
    print("Тестирование парсера сообщений:")
    print("=" * 60)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n{i}. Исходное сообщение:")
        print(f"   {message}")
        
        parsed = parser.parse_message(message)
        if parsed:
            print(f"   ✓ Успешно распарсено:")
            print(f"     Время: {parsed.timestamp}")
            print(f"     Chat ID: {parsed.chat_id}")
            print(f"     Пользователь: {parsed.username}")
            print(f"     Время в чате: {parsed.time_in_chat}")
            print(f"     Сообщение: {parsed.message}")
            print(f"     Продолжение: {parsed.is_continuation}")
            print(f"     FROM сообщение: {parsed.is_from_message}")
            print(f"     TO сообщение: {parsed.is_to_message}")
            if parsed.original_sender:
                print(f"     Оригинальный отправитель: {parsed.original_sender}")
            if parsed.original_recipient:
                print(f"     Оригинальный получатель: {parsed.original_recipient}")
            
            # Тестируем методы получения имен
            display_name = parser.get_display_username(parsed)
            channel_name = parser.get_channel_name(parsed)
            print(f"     Отображаемое имя: {display_name}")
            print(f"     Имя канала: {channel_name}")
        else:
            print(f"   ✗ Не удалось распарсить")

def test_with_real_file():
    """Тестирует парсер на реальном файле"""
    parser = BattlenetMessageParser()
    
    try:
        with open('../battlenet_messages.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"\nТестирование на реальном файле ({len(lines)} строк):")
        print("=" * 60)
        
        parsed_count = 0
        failed_count = 0
        users = set()
        
        for line in lines[:20]:  # Тестируем первые 20 строк
            line = line.strip()
            if not line:
                continue
                
            parsed = parser.parse_message(line)
            if parsed:
                parsed_count += 1
                display_name = parser.get_display_username(parsed)
                channel_name = parser.get_channel_name(parsed)
                users.add(channel_name)
                
                print(f"✓ {display_name} → #{channel_name}: {parsed.message[:50]}...")
            else:
                failed_count += 1
                print(f"✗ Не удалось распарсить: {line[:50]}...")
        
        print(f"\nСтатистика:")
        print(f"Успешно распарсено: {parsed_count}")
        print(f"Не удалось распарсить: {failed_count}")
        print(f"Уникальных пользователей: {len(users)}")
        print(f"Пользователи: {', '.join(sorted(users))}")
        
    except FileNotFoundError:
        print("Файл ../battlenet_messages.txt не найден")
    except Exception as e:
        print(f"Ошибка при чтении файла: {e}")

if __name__ == "__main__":
    test_parser()
    test_with_real_file()
