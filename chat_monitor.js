/**
 * JavaScript код для мониторинга чата Battle.net
 * Внедряется через Chrome DevTools Protocol
 */

(function() {
    'use strict';
    
    // Проверка, что скрипт не был уже внедрен
    if (window.battleNetChatMonitor) {
        console.log('🔄 Chat monitor уже запущен, перезапуск...');
        window.battleNetChatMonitor.stop();
    }
    
    console.log('🚀 Запуск Battle.net Chat Monitor...');
    
    const ChatMonitor = {
        isRunning: false,
        observer: null,
        serverUrl: 'http://localhost:8080/chat-message',
        processedMessages: new Set(),
        messageSelectors: [
            // Основной селектор для сообщений Battle.net
            '.chat-message'
        ],
        isInitialized: false,
        
        init() {
            console.log('🔧 Инициализация Chat Monitor...');

            // Сначала помечаем все существующие сообщения как обработанные
            this.markExistingMessagesAsProcessed();

            this.setupMessageObserver();
            this.setupPeriodicScan();
            this.isRunning = true;
            this.isInitialized = true;
            console.log('✅ Chat Monitor запущен');
            console.log('🚫 Существующие сообщения проигнорированы, обрабатываются только новые');
        },
        
        stop() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.isRunning = false;
            console.log('🛑 Chat Monitor остановлен');
        },
        
        setupMessageObserver() {
            // Создание MutationObserver для отслеживания изменений DOM
            this.observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    // Обработка добавленных узлов
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.processElement(node);
                        }
                    });
                    
                    // Обработка изменений текста
                    if (mutation.type === 'characterData' && mutation.target.parentNode) {
                        this.processElement(mutation.target.parentNode);
                    }
                });
            });
            
            // Запуск наблюдения за всем документом
            this.observer.observe(document.body, {
                childList: true,
                subtree: true,
                characterData: true,
                attributes: false
            });
            
            console.log('👁️ DOM Observer настроен');
        },
        
        setupPeriodicScan() {
            // Периодическое сканирование для поиска пропущенных сообщений
            // НО только после инициализации, чтобы не захватывать старые сообщения
            setInterval(() => {
                if (this.isRunning && this.isInitialized) {
                    this.scanForMessages();
                }
            }, 2000); // Каждые 2 секунды

            // Проверка команд отправки
            setInterval(() => {
                if (this.isRunning) {
                    this.checkSendCommands();
                }
            }, 1000); // Каждую секунду
        },

        markExistingMessagesAsProcessed() {
            // Помечаем все существующие сообщения как уже обработанные
            console.log('🔍 Поиск существующих сообщений для игнорирования...');

            let markedCount = 0;
            this.messageSelectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        const msgId = element.getAttribute('data-msg-id');
                        if (msgId && !this.processedMessages.has(msgId)) {
                            this.processedMessages.add(msgId);
                            markedCount++;
                        }
                    });
                } catch (e) {
                    // Игнорируем ошибки селекторов
                }
            });

            console.log(`🚫 Помечено ${markedCount} существующих сообщений как обработанные`);
        },

        scanForMessages() {
            // Поиск сообщений по всем возможным селекторам
            this.messageSelectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        this.processElement(element);
                    });
                } catch (e) {
                    // Игнорируем ошибки селекторов
                }
            });
        },
        
        processElement(element) {
            // Проверяем, что это именно chat-message элемент
            if (!element || !element.classList.contains('chat-message')) return;

            // Извлекаем данные сообщения
            const messageData = this.extractBattleNetMessage(element);
            if (!messageData) return;

            // Проверка на дублирование по data-msg-id
            const msgId = element.getAttribute('data-msg-id');
            if (msgId && this.processedMessages.has(msgId)) return;

            // Отправка сообщения на сервер
            this.sendMessage(messageData);

            // Запоминание обработанного сообщения
            if (msgId) {
                this.processedMessages.add(msgId);

                // Ограничение размера кэша
                if (this.processedMessages.size > 1000) {
                    const firstItem = this.processedMessages.values().next().value;
                    this.processedMessages.delete(firstItem);
                }
            }
        },
        
        isValidMessage(text) {
            // Фильтрация валидных сообщений чата
            if (!text || text.length < 1) return false;
            if (text.length > 1000) return false; // Слишком длинные сообщения
            
            // Исключение системных сообщений
            const systemPatterns = [
                /^Battle\.net/i,
                /^Blizzard/i,
                /^©/,
                /^\d{1,2}:\d{2}$/,  // Только время
                /^Loading/i,
                /^Connecting/i,
                /^Error/i,
                /^Warning/i,
                /^Info/i,
                /^Debug/i
            ];
            
            for (let pattern of systemPatterns) {
                if (pattern.test(text)) return false;
            }
            
            // Исключение повторяющихся символов
            if (/^(.)\1{10,}$/.test(text)) return false;
            
            return true;
        },
        
        extractBattleNetMessage(element) {
            // Извлекаем текст сообщения
            const msgElement = element.querySelector('.msg .msg-wrapper span');
            if (!msgElement) return null;

            const messageText = msgElement.textContent.trim();
            if (!messageText || messageText.length === 0) return null;

            // Проверяем, есть ли информация об отправителе (не rollup сообщение)
            const isRollup = element.classList.contains('rollup');
            let sender = null;
            let timestamp = null;

            if (!isRollup) {
                // Извлекаем отправителя
                const senderElement = element.querySelector('.header .sender .author');
                if (senderElement) {
                    sender = senderElement.textContent.trim();
                }

                // Извлекаем время
                const timestampElement = element.querySelector('.header .sender .timestamp');
                if (timestampElement) {
                    timestamp = timestampElement.textContent.trim();
                }
            }

            // Извлекаем ID пользователя из URL для идентификации чата
            const urlMatch = window.location.href.match(/\/whisper\/w:(\d+)/);
            const chatId = urlMatch ? urlMatch[1] : 'unknown';

            const data = {
                text: messageText,
                sender: sender,
                timestamp: timestamp,
                isRollup: isRollup,
                msgId: element.getAttribute('data-msg-id'),
                chatId: chatId,
                type: 'battlenet_message',
                url: window.location.href,
                captureTime: Date.now()
            };

            return data;
        },
        
        extractSender(element) {
            // Поиск имени отправителя в родительских элементах
            let current = element;
            for (let i = 0; i < 5 && current; i++) {
                // Поиск элементов с именем пользователя
                const senderElement = current.querySelector('[class*="sender"], [class*="author"], [class*="username"]');
                if (senderElement) {
                    const senderText = senderElement.textContent.trim();
                    if (senderText && senderText.length < 50) {
                        return senderText;
                    }
                }
                current = current.parentElement;
            }
            
            // Поиск паттерна username#1234
            const usernameMatch = element.textContent.match(/(\w+#\d{4})/);
            if (usernameMatch) {
                return usernameMatch[1];
            }
            
            return null;
        },
        
        detectMessageType(element, text) {
            const className = element.className.toLowerCase();
            
            if (className.includes('whisper')) return 'whisper';
            if (className.includes('private')) return 'private';
            if (className.includes('system')) return 'system';
            if (className.includes('notification')) return 'notification';
            
            return 'chat';
        },
        
        getMessageHash(text) {
            // Простое хеширование для предотвращения дублирования
            let hash = 0;
            for (let i = 0; i < text.length; i++) {
                const char = text.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Конвертация в 32-битное число
            }
            return hash.toString();
        },
        
        async sendMessage(data) {
            try {
                const response = await fetch(this.serverUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const logMsg = data.sender
                        ? `📤 ${data.sender} (${data.timestamp}): ${data.text.substring(0, 50)}${data.text.length > 50 ? '...' : ''}`
                        : `📤 [rollup]: ${data.text.substring(0, 50)}${data.text.length > 50 ? '...' : ''}`;
                    console.log(logMsg);
                } else {
                    console.error('❌ Ошибка отправки сообщения:', response.status);
                }
            } catch (error) {
                console.error('❌ Ошибка сети при отправке сообщения:', error);
            }
        },

        // Новые функции для отправки сообщений
        findInputBox() {
            // Поиск поля ввода сообщения
            const selectors = [
                '.edit-box[contenteditable="true"]',
                '[contenteditable="true"][placeholder*="message"]',
                '[role="textbox"][contenteditable="true"]',
                'div[contenteditable="true"].edit-box'
            ];

            for (let selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    console.log('✅ Найдено поле ввода:', selector);
                    return element;
                }
            }

            console.error('❌ Поле ввода не найдено');
            return null;
        },

        async sendChatMessage(messageText) {
            const inputBox = this.findInputBox();
            if (!inputBox) {
                console.error('❌ Не удалось найти поле ввода');
                return false;
            }

            try {
                // Очищаем поле ввода
                inputBox.innerHTML = '';
                inputBox.textContent = '';

                // Устанавливаем фокус
                inputBox.focus();

                // Вводим текст
                inputBox.innerHTML = messageText;
                inputBox.textContent = messageText;

                // Обновляем value атрибут если есть
                if (inputBox.hasAttribute('value')) {
                    inputBox.setAttribute('value', messageText);
                }

                // Генерируем события ввода
                const inputEvent = new Event('input', { bubbles: true });
                inputBox.dispatchEvent(inputEvent);

                const changeEvent = new Event('change', { bubbles: true });
                inputBox.dispatchEvent(changeEvent);

                // Ждем немного для обработки
                await new Promise(resolve => setTimeout(resolve, 100));

                // Отправляем сообщение нажатием Enter
                const enterEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                });

                inputBox.dispatchEvent(enterEvent);

                console.log('📨 Отправлено сообщение:', messageText);
                return true;

            } catch (error) {
                console.error('❌ Ошибка отправки сообщения:', error);
                return false;
            }
        },

        async checkSendCommands() {
            // Проверяем команды отправки для текущего чата
            const urlMatch = window.location.href.match(/\/whisper\/w:(\d+)/);
            const currentChatId = urlMatch ? urlMatch[1] : null;

            if (!currentChatId) return;

            try {
                const response = await fetch(`http://localhost:8080/get-commands?chatId=${currentChatId}`);
                if (response.ok) {
                    const commands = await response.json();

                    for (let command of commands) {
                        if (command.action === 'send_message' && command.chatId === currentChatId) {
                            console.log('📬 Получена команда отправки:', command.message);
                            await this.sendChatMessage(command.message);
                        }
                    }
                }
            } catch (error) {
                // Игнорируем ошибки сети для команд
            }
        }
    };
    
    // Глобальная регистрация для управления
    window.battleNetChatMonitor = ChatMonitor;
    
    // Запуск мониторинга
    ChatMonitor.init();
    
    // Информация о статусе
    console.log('✅ Battle.net Chat Monitor готов к работе');
    console.log('📡 Сервер:', ChatMonitor.serverUrl);
    console.log('🔍 Селекторы:', ChatMonitor.messageSelectors);
    
})();
