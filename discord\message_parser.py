import re
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass

@dataclass
class ParsedMessage:
    """Структура для хранения распарсенного сообщения"""
    timestamp: datetime
    chat_id: str
    username: str
    time_in_chat: str
    message: str
    is_continuation: bool = False
    is_from_message: bool = False
    is_to_message: bool = False
    original_sender: Optional[str] = None
    original_recipient: Optional[str] = None

class BattlenetMessageParser:
    """Парсер для сообщений из battlenet_messages.txt"""
    
    def __init__(self):
        # Регулярное выражение для основных сообщений
        self.main_pattern = re.compile(
            r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \[Chat:(\d+)\] ([^(]+) \(([^)]+)\): (.+)'
        )
        
        # Регулярное выражение для продолжений
        self.continuation_pattern = re.compile(
            r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \[Chat:(\d+)\] \[продолжение\]: (.+)'
        )
        
        # Паттерн для новых FROM сообщений: [19:39:01] {FROM Товчикус-Гордунни}: ф
        self.new_from_pattern = re.compile(r'\[(\d{2}:\d{2}:\d{2})\] \{FROM ([^}]+)\}: (.+)')

        # Старые паттерны для совместимости
        self.from_pattern = re.compile(r'\[FROM: ([^\]]+)\] (.+)')
        self.to_pattern = re.compile(r'\[TO: ([^\]]+)\] (.+)')
    
    def parse_message(self, line: str) -> Optional[ParsedMessage]:
        """Парсит одну строку сообщения"""
        line = line.strip()
        if not line:
            return None

        # Попробуем сначала основной паттерн
        match = self.main_pattern.match(line)
        if match:
            timestamp_str, chat_id, username, time_in_chat, message = match.groups()
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            username = username.strip()

            # Проверим новый паттерн FROM: [19:30:37] FROM Товчикус-Гордунни: фыва
            new_from_match = self.new_from_pattern.match(message)
            if new_from_match:
                game_time, original_sender, actual_message = new_from_match.groups()
                return ParsedMessage(
                    timestamp=timestamp,
                    chat_id=chat_id,
                    username=username,
                    time_in_chat=game_time,  # Используем время из игры
                    message=actual_message,
                    is_from_message=True,
                    original_sender=original_sender
                )

            # Проверим старые паттерны для совместимости
            from_match = self.from_pattern.match(message)
            to_match = self.to_pattern.match(message)

            if from_match:
                original_sender, actual_message = from_match.groups()
                return ParsedMessage(
                    timestamp=timestamp,
                    chat_id=chat_id,
                    username=username,
                    time_in_chat=time_in_chat,
                    message=actual_message,
                    is_from_message=True,
                    original_sender=original_sender
                )
            elif to_match:
                original_recipient, actual_message = to_match.groups()
                return ParsedMessage(
                    timestamp=timestamp,
                    chat_id=chat_id,
                    username=username,
                    time_in_chat=time_in_chat,
                    message=actual_message,
                    is_to_message=True,
                    original_recipient=original_recipient
                )
            else:
                # Игнорируем обычные сообщения, обрабатываем только FROM
                return None

        # Попробуем паттерн продолжения
        match = self.continuation_pattern.match(line)
        if match:
            timestamp_str, chat_id, message = match.groups()
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')

            # Проверим новый паттерн FROM в продолжении
            new_from_match = self.new_from_pattern.match(message)
            if new_from_match:
                game_time, original_sender, actual_message = new_from_match.groups()
                return ParsedMessage(
                    timestamp=timestamp,
                    chat_id=chat_id,
                    username="[продолжение]",
                    time_in_chat=game_time,
                    message=actual_message,
                    is_continuation=True,
                    is_from_message=True,
                    original_sender=original_sender
                )

            # Проверим старые паттерны для совместимости
            from_match = self.from_pattern.match(message)
            to_match = self.to_pattern.match(message)

            if from_match:
                original_sender, actual_message = from_match.groups()
                return ParsedMessage(
                    timestamp=timestamp,
                    chat_id=chat_id,
                    username="[продолжение]",
                    time_in_chat="",
                    message=actual_message,
                    is_continuation=True,
                    is_from_message=True,
                    original_sender=original_sender
                )
            elif to_match:
                original_recipient, actual_message = to_match.groups()
                return ParsedMessage(
                    timestamp=timestamp,
                    chat_id=chat_id,
                    username="[продолжение]",
                    time_in_chat="",
                    message=actual_message,
                    is_continuation=True,
                    is_to_message=True,
                    original_recipient=original_recipient
                )
            else:
                # Игнорируем продолжения без FROM/TO
                return None

        return None
    
    def get_display_username(self, parsed_msg: ParsedMessage) -> str:
        """Получает имя пользователя для отображения в Discord"""
        if parsed_msg.is_from_message and parsed_msg.original_sender:
            return parsed_msg.original_sender
        elif parsed_msg.is_continuation:
            return "Продолжение"
        else:
            return parsed_msg.username
    
    def get_channel_name(self, parsed_msg: ParsedMessage) -> str:
        """Получает имя канала для сообщения"""
        if parsed_msg.is_from_message and parsed_msg.original_sender:
            # Используем оригинальное имя, убираем только недопустимые символы
            name = parsed_msg.original_sender.lower()
            # Заменяем только действительно недопустимые символы
            name = re.sub(r'[^\w\-а-яё]', '-', name, flags=re.UNICODE)
            name = re.sub(r'-+', '-', name)  # Убираем множественные дефисы
            name = name.strip('-')  # Убираем дефисы в начале и конце
            return name or "unknown-user"
        else:
            # Для обычных сообщений используем username
            name = parsed_msg.username.lower()
            name = re.sub(r'[^\w\-а-яё]', '-', name, flags=re.UNICODE)
            name = re.sub(r'-+', '-', name)
            name = name.strip('-')
            return name or "unknown-user"

