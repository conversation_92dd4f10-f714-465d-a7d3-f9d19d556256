@echo off
title WoW Discord Bot
echo Запуск WoW Discord Bot...
echo.

REM Проверяем наличие .env файла
if not exist ".env" (
    echo Ошибка: Файл .env не найден!
    echo.
    echo Создайте файл .env на основе .env.example:
    echo 1. Скопируйте .env.example в .env
    echo 2. Отредактируйте .env с вашими настройками
    echo.
    pause
    exit /b 1
)

REM Проверяем наличие Python
py --version >nul 2>&1
if errorlevel 1 (
    echo Ошибка: Python не найден!
    echo Установите Python 3.8 или выше с https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Запускаем бота...
echo Для остановки нажмите Ctrl+C
echo.

py main.py

echo.
echo Бот остановлен.
pause
