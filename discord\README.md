# WoW Discord Bot

Discord бот для пересылки сообщений из World of Warcraft через Battle.net в Discord каналы.

## Функциональность

- **Мониторинг файла**: Отслеживает изменения в `battlenet_messages.txt` в реальном времени
- **Автоматическое создание каналов**: Создает отдельный канал для каждого пользователя
- **Парсинг сообщений**: Обрабатывает различные типы сообщений (обычные, продолжения, FROM/TO)
- **Группировка сообщений**: Правильно группирует продолжения сообщений
- **Сохранение состояния**: Запоминает созданные каналы между перезапусками

## Установка

1. Установите зависимости:
```bash
pip install -r requirements.txt
```

2. Создайте Discord приложение и бота:
   - Перейдите на https://discord.com/developers/applications
   - Создайте новое приложение
   - В разделе "Bot" создайте бота и скопируйте токен
   - В разделе "OAuth2 > URL Generator" выберите scopes: `bot` и permissions: `Manage Channels`, `Send Messages`, `Read Message History`

3. Настройте переменные окружения:
```bash
cp .env.example .env
```

Отредактируйте `.env` файл:
```env
DISCORD_TOKEN=your_bot_token_here
GUILD_ID=your_guild_id_here
BATTLENET_MESSAGES_PATH=../battlenet_messages.txt
CATEGORY_NAME=WoW Messages
```

4. Пригласите бота на сервер используя сгенерированную OAuth2 ссылку

## Запуск

```bash
python main.py
```

## Команды бота

- `!status` - Показывает статус бота и количество активных каналов
- `!channels` - Показывает список каналов пользователей
- `!chatids` - Показывает сопоставления пользователей с chat_id
- `!mappings` - Показывает содержимое файла сопоставлений
- `!restore` - Восстанавливает chat_id для существующих каналов

## Структура сообщений

Бот обрабатывает следующие форматы сообщений из `battlenet_messages.txt`:

### Обычные сообщения
```
[2025-07-15 14:30:25] [Chat:403426477] towner (2:26 PM): Привет!
```

### Продолжения сообщений
```
[2025-07-15 14:30:25] [Chat:403426477] [продолжение]: Это продолжение сообщения
```

### FROM сообщения (новый формат - от других пользователей через аддон)
```
[2025-07-15 15:40:26] [Chat:403426477] [продолжение]: [19:40:25] {FROM Товчикус-Гордунни}: алооооооооо
```

### FROM сообщения (старый формат - для совместимости)
```
[2025-07-15 14:30:25] [Chat:403426477] towner (2:26 PM): [FROM: TestSender] Сообщение от TestSender
```

### TO сообщения (отправленные через аддон)
```
[2025-07-15 14:30:25] [Chat:403426477] towner (2:26 PM): [TO: TestSender] Ответ для TestSender
```

## Логика работы

1. **Мониторинг файла**: Бот отслеживает изменения в файле `battlenet_messages.txt`
2. **Парсинг**: Каждая новая строка парсится для извлечения информации о сообщении
3. **Определение пользователя**: 
   - Для FROM сообщений используется имя из `[FROM: username]`
   - Для обычных сообщений используется имя отправителя
   - Для продолжений используется последний известный пользователь
4. **Создание каналов**: Для каждого уникального пользователя создается отдельный канал
5. **Отправка сообщений**: Сообщения отправляются в соответствующий канал пользователя
6. **Двусторонняя связь**: Сообщения из Discord каналов отправляются обратно в Battle.net с форматом `{username} message`

## Файлы

- `main.py` - Точка входа в приложение
- `discord_bot.py` - Основной класс Discord бота
- `message_parser.py` - Парсер сообщений из battlenet_messages.txt
- `file_monitor.py` - Мониторинг изменений файла
- `requirements.txt` - Зависимости Python
- `.env.example` - Пример файла конфигурации
- `discord_bot_state.json` - Файл состояния (создается автоматически)
- `chat_mappings.txt` - Сопоставления пользователей и chat_id (создается автоматически)

## Примечания

- Бот автоматически создает категорию "WoW Messages" (или другую, указанную в настройках)
- Имена каналов генерируются из имен пользователей с заменой недопустимых символов
- Состояние бота сохраняется в JSON файл для восстановления после перезапуска
- Бот обрабатывает как новые сообщения, так и существующие при запуске (последние 50)
