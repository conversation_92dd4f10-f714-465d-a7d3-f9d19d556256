#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Упрощенный скрипт для внедрения JavaScript мониторинга чата в Battle.net
Требует ручного запуска Battle.net с debug портом
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# Импорт наших модулей
from battlenet_message_server import MessageServer
from battlenet_cdp_client import BattleNetCDPClient

class ManualBattleNetInjector:
    def __init__(self):
        self.server = MessageServer()
        self.cdp_client = BattleNetCDPClient()
        self.js_code = None
        self.running = False
        
    def load_javascript_code(self):
        """Загрузка JavaScript кода из файла"""
        js_file = Path("chat_monitor.js")
        
        if not js_file.exists():
            print("❌ Файл chat_monitor.js не найден!")
            return False
        
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                self.js_code = f.read()
            print("✅ JavaScript код загружен")
            return True
        except Exception as e:
            print(f"❌ Ошибка загрузки JavaScript: {e}")
            return False
    
    def print_instructions(self):
        """Вывод инструкций для пользователя"""
        print("\n" + "=" * 70)
        print("📋 ИНСТРУКЦИИ ПО ЗАПУСКУ")
        print("=" * 70)
        print("1. Закройте Battle.net если он запущен")
        print("2. Откройте командную строку как администратор")
        print("3. Выполните команду:")
        print('   "C:\\Program Files (x86)\\Battle.net\\Battle.net.exe" --remote-debugging-port=9222')
        print("4. Дождитесь полной загрузки Battle.net")
        print("5. Откройте чат или whisper окно в Battle.net")
        print("6. Нажмите Enter в этом окне для продолжения...")
        print("=" * 70)
        
        input("Нажмите Enter когда Battle.net будет готов...")
    
    async def run(self):
        """Основной цикл работы"""
        print("🎮 Battle.net Manual Chat Injector")
        print("=" * 50)
        
        # Загрузка JavaScript кода
        if not self.load_javascript_code():
            return False
        
        # Запуск HTTP сервера
        print("🚀 Запуск HTTP сервера...")
        if not self.server.start():
            print("❌ Не удалось запустить HTTP сервер")
            return False
        print("✅ HTTP сервер запущен на http://localhost:8080")
        
        # Инструкции для пользователя
        self.print_instructions()
        
        # Подключение к Battle.net
        print("🔌 Подключение к Battle.net...")
        if not await self.cdp_client.connect():
            print("❌ Не удалось подключиться к Battle.net")
            print("💡 Убедитесь, что Battle.net запущен с --remote-debugging-port=9222")
            return False
        
        # Внедрение JavaScript
        print("💉 Внедрение JavaScript мониторинга...")
        if not await self.cdp_client.inject_javascript(self.js_code):
            print("❌ Не удалось внедрить JavaScript")
            return False
        
        print("\n" + "=" * 70)
        print("🎉 СИСТЕМА УСПЕШНО ЗАПУЩЕНА!")
        print("=" * 70)
        print("📝 Сообщения сохраняются в: battlenet_messages.txt")
        print("📊 Статус сервера: http://localhost:8080/status")
        print("🔧 Для остановки нажмите Ctrl+C")
        print("💬 Теперь отправьте или получите сообщения в Battle.net")
        print("=" * 70)
        
        self.running = True
        
        try:
            # Основной цикл мониторинга
            while self.running:
                await asyncio.sleep(1)
                
                # Проверка состояния сервера
                if not self.server.is_running():
                    print("⚠️ HTTP сервер остановлен, перезапуск...")
                    self.server.start()
                
                # Проверка подключения к Battle.net
                if not self.cdp_client.connected:
                    print("⚠️ Потеряно подключение к Battle.net")
                    print("💡 Попробуйте перезапустить Battle.net с debug портом")
                    break
        
        except KeyboardInterrupt:
            print("\n🛑 Получен сигнал остановки...")
        
        finally:
            await self.cleanup()
        
        return True
    
    async def cleanup(self):
        """Очистка ресурсов"""
        print("🧹 Очистка ресурсов...")
        
        self.running = False
        
        # Остановка CDP клиента
        if self.cdp_client.connected:
            await self.cdp_client.disconnect()
        
        # Остановка HTTP сервера
        if self.server.is_running():
            self.server.stop()
        
        print("✅ Очистка завершена")


def check_files():
    """Проверка наличия необходимых файлов"""
    required_files = [
        'chat_monitor.js', 
        'battlenet_message_server.py', 
        'battlenet_cdp_client.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Отсутствуют файлы:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True


def check_dependencies():
    """Проверка зависимостей"""
    required_modules = ['websockets', 'aiohttp', 'psutil']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ Отсутствуют зависимости:")
        for module in missing_modules:
            print(f"   - {module}")
        print(f"\n💡 Установите: py -m pip install {' '.join(missing_modules)}")
        return False
    
    return True


async def main():
    """Главная функция"""
    print("🔍 Проверка системы...")
    
    if not check_dependencies():
        return
    
    if not check_files():
        return
    
    print("✅ Все проверки пройдены")
    
    # Запуск инжектора
    injector = ManualBattleNetInjector()
    await injector.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 До свидания!")
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()
