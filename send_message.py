#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для отправки сообщений в Battle.net чат через систему мониторинга
"""

import requests
import json
import sys
import os

# Устанавливаем кодировку для Windows
if os.name == 'nt':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

def send_message_to_chat(chat_id, message):
    """Отправка сообщения в указанный чат"""
    url = "http://localhost:8080/send-message"
    
    data = {
        "chatId": chat_id,
        "message": message
    }
    
    try:
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"[OK] Сообщение отправлено в чат {chat_id}: {message}")
                return True
            else:
                print(f"[ERROR] Ошибка: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"[ERROR] HTTP ошибка: {response.status_code}")
            return False

    except requests.exceptions.ConnectionError:
        print("[ERROR] Не удалось подключиться к серверу. Убедитесь, что система мониторинга запущена.")
        return False
    except Exception as e:
        print(f"[ERROR] Ошибка: {e}")
        return False

def main():
    """Главная функция"""
    if len(sys.argv) < 3:
        print("Использование:")
        print("  python send_message.py <chat_id> <message>")
        print("")
        print("Примеры:")
        print("  python send_message.py 403426477 'Привет!'")
        print("  python send_message.py 1126056253 'Как дела?'")
        print("")
        print("Для получения chat_id посмотрите в логи системы мониторинга")
        return
    
    chat_id = sys.argv[1]
    message = ' '.join(sys.argv[2:])
    
    if not message.strip():
        print("[ERROR] Сообщение не может быть пустым")
        return

    print(f"[SEND] Отправка сообщения в чат {chat_id}...")
    send_message_to_chat(chat_id, message)

if __name__ == "__main__":
    main()
