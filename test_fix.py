#!/usr/bin/env python3
"""
Тестовый скрипт для проверки исправления проблемы с дублированием сообщений
"""

import os
import json
import time

def test_file_position():
    """Тестирует работу с позицией файла"""
    
    # Путь к файлу состояния
    state_file = "discord/discord_bot_state.json"
    messages_file = "battlenet_messages.txt"
    
    print("=== Тест исправления дублирования сообщений ===")
    print()
    
    # Проверяем текущее состояние
    if os.path.exists(state_file):
        with open(state_file, 'r', encoding='utf-8') as f:
            state = json.load(f)
        
        print(f"Текущее состояние бота:")
        print(f"  - Каналов пользователей: {len(state.get('user_channels', {}))}")
        print(f"  - Сопоставлений chat_id: {len(state.get('user_chat_ids', {}))}")
        print(f"  - Позиция в файле: {state.get('last_processed_position', 'НЕ УСТАНОВЛЕНА')}")
        print()
    else:
        print("❌ Файл состояния не найден!")
        return
    
    # Проверяем размер файла сообщений
    if os.path.exists(messages_file):
        file_size = os.path.getsize(messages_file)
        print(f"Размер файла сообщений: {file_size} байт")
        
        last_pos = state.get('last_processed_position', 0)
        if last_pos >= file_size:
            print("✅ Позиция файла установлена корректно (на конец или дальше)")
            print("   Старые сообщения не будут обрабатываться повторно")
        else:
            print(f"⚠️  Позиция файла ({last_pos}) меньше размера файла ({file_size})")
            print("   Это может привести к повторной обработке сообщений")
        print()
    else:
        print("❌ Файл сообщений не найден!")
        return
    
    # Симулируем добавление нового сообщения
    print("Симулируем добавление нового сообщения...")
    test_message = f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] TEST: Тестовое сообщение для проверки\n"
    
    with open(messages_file, 'a', encoding='utf-8') as f:
        f.write(test_message)
    
    new_file_size = os.path.getsize(messages_file)
    print(f"Новый размер файла: {new_file_size} байт")
    print(f"Добавлено байт: {new_file_size - file_size}")
    print()
    
    print("=== Результат ===")
    print("✅ Исправление применено!")
    print("📋 Что изменилось:")
    print("   1. Добавлено отслеживание позиции в файле")
    print("   2. При запуске бот НЕ обрабатывает старые сообщения")
    print("   3. Обрабатываются только НОВЫЕ сообщения после запуска")
    print("   4. Добавлены команды !reset_position и !reprocess для управления")
    print()
    print("🚀 Теперь можно перезапускать бота без дублирования сообщений!")

if __name__ == "__main__":
    test_file_position()
