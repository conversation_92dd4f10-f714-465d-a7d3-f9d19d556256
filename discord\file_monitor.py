import asyncio
import os
from typing import Callable, Optional
from datetime import datetime

class FileMonitor:
    """Мониторинг изменений в файле battlenet_messages.txt"""
    
    def __init__(self, file_path: str, callback: Callable[[str], None]):
        self.file_path = file_path
        self.callback = callback
        self.last_position = 0
        self.last_size = 0
        self.is_running = False
        
    async def start_monitoring(self):
        """Запускает мониторинг файла"""
        self.is_running = True
        
        # Инициализируем позицию в файле
        if os.path.exists(self.file_path):
            with open(self.file_path, 'r', encoding='utf-8') as f:
                f.seek(0, 2)  # Переходим в конец файла
                self.last_position = f.tell()
                self.last_size = os.path.getsize(self.file_path)
        
        print(f"Начинаем мониторинг файла: {self.file_path}")
        print(f"Начальная позиция: {self.last_position}")
        
        while self.is_running:
            try:
                await self._check_file_changes()
                await asyncio.sleep(1)  # Проверяем каждую секунду
            except Exception as e:
                print(f"Ошибка при мониторинге файла: {e}")
                await asyncio.sleep(5)  # Ждем дольше при ошибке
    
    async def _check_file_changes(self):
        """Проверяет изменения в файле"""
        if not os.path.exists(self.file_path):
            return
        
        current_size = os.path.getsize(self.file_path)
        
        # Если файл стал меньше, значит он был перезаписан
        if current_size < self.last_size:
            print("Файл был перезаписан, сбрасываем позицию")
            self.last_position = 0
        
        # Если размер не изменился, ничего не делаем
        if current_size == self.last_size:
            return
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                f.seek(self.last_position)
                new_content = f.read()
                
                if new_content:
                    # Разбиваем на строки и обрабатываем каждую
                    lines = new_content.split('\n')
                    
                    # Если последняя строка пустая (файл заканчивается переносом), убираем её
                    if lines and not lines[-1].strip():
                        lines = lines[:-1]
                    
                    for line in lines:
                        if line.strip():
                            await self._process_new_line(line)
                
                self.last_position = f.tell()
                self.last_size = current_size
                
        except Exception as e:
            print(f"Ошибка при чтении файла: {e}")
    
    async def _process_new_line(self, line: str):
        """Обрабатывает новую строку из файла"""
        try:
            # Вызываем callback функцию
            if asyncio.iscoroutinefunction(self.callback):
                await self.callback(line)
            else:
                self.callback(line)
        except Exception as e:
            print(f"Ошибка при обработке строки '{line}': {e}")
    
    def stop_monitoring(self):
        """Останавливает мониторинг"""
        self.is_running = False
        print("Мониторинг файла остановлен")
    
    async def read_existing_messages(self, limit: Optional[int] = None) -> list:
        """Читает существующие сообщения из файла"""
        messages = []
        
        if not os.path.exists(self.file_path):
            return messages
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                # Если указан лимит, берем последние N строк
                if limit:
                    lines = lines[-limit:]
                
                for line in lines:
                    line = line.strip()
                    if line:
                        messages.append(line)
                        
        except Exception as e:
            print(f"Ошибка при чтении существующих сообщений: {e}")
        
        return messages
