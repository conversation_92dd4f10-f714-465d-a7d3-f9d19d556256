#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome DevTools Protocol клиент для подключения к Battle.net
"""

import asyncio
import websockets
import json
import aiohttp
import time
import subprocess
import os
import psutil
from pathlib import Path

class WhisperConnection:
    """Класс для управления одним подключением к whisper вкладке"""
    def __init__(self, tab, debug_port=9222):
        self.tab = tab
        self.debug_port = debug_port
        self.ws = None
        self.connected = False
        self.url = tab.get('url', '')
        self.ws_url = tab.get('webSocketDebuggerUrl', '')

    async def connect(self):
        """Подключение к конкретной whisper вкладке"""
        if not self.ws_url:
            print(f"❌ WebSocket URL не найден для {self.url}")
            return False

        try:
            print(f"🔌 Подключение к whisper: {self.url}")
            self.ws = await websockets.connect(self.ws_url)
            self.connected = True
            return True
        except Exception as e:
            print(f"❌ Ошибка подключения к {self.url}: {e}")
            return False

    async def send_command(self, method, params=None):
        """Отправка команды через CDP"""
        if not self.connected or not self.ws:
            return None

        if not hasattr(self, '_command_id'):
            self._command_id = 1
        else:
            self._command_id += 1

        message = {
            "id": self._command_id,
            "method": method,
            "params": params or {}
        }

        try:
            await self.ws.send(json.dumps(message))
            response = await self.ws.recv()
            return json.loads(response)
        except Exception as e:
            print(f"❌ Ошибка команды для {self.url}: {e}")
            return None

    async def enable_runtime(self):
        """Включение Runtime домена"""
        result = await self.send_command("Runtime.enable")
        return result and not result.get('error')

    async def inject_javascript(self, js_code):
        """Внедрение JavaScript кода"""
        if not await self.enable_runtime():
            return False

        result = await self.send_command("Runtime.evaluate", {
            "expression": js_code,
            "returnByValue": True
        })

        return result and not result.get('error')

    async def disconnect(self):
        """Отключение"""
        if self.ws:
            await self.ws.close()
            self.connected = False


class BattleNetCDPClient:
    def __init__(self, debug_port=9222):
        self.debug_port = debug_port
        self.connections = []
        self.connected = False
        
    async def find_battlenet_process(self):
        """Поиск процесса Battle.net"""
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if 'battle.net' in proc.info['name'].lower():
                    return proc.info
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def find_battlenet_executable(self):
        """Поиск исполняемого файла Battle.net"""
        possible_paths = [
            "C:\\Program Files (x86)\\Battle.net\\Battle.net.exe",
            "C:\\Program Files\\Battle.net\\Battle.net.exe",
            os.path.expanduser("~\\AppData\\Local\\Battle.net\\Battle.net.exe")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def close_battlenet(self):
        """Закрытие всех процессов Battle.net"""
        print("🔄 Закрытие Battle.net...")

        processes_to_close = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'battle.net' in proc.info['name'].lower():
                    processes_to_close.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # Сначала пытаемся мягко закрыть
        for proc_info in processes_to_close:
            try:
                proc = psutil.Process(proc_info['pid'])
                proc.terminate()
                print(f"   Закрываю процесс: {proc_info['name']} (PID: {proc_info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # Ждем завершения
        time.sleep(3)

        # Принудительно закрываем оставшиеся
        for proc_info in processes_to_close:
            try:
                proc = psutil.Process(proc_info['pid'])
                if proc.is_running():
                    proc.kill()
                    print(f"   Принудительно закрыт: {proc_info['name']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    
    def start_battlenet_debug(self):
        """Запуск Battle.net с debug портом"""
        print("⚠️ Battle.net нужно запустить вручную с debug портом!")
        print(f"📋 Выполните команду:")
        print(f'   "C:\\Program Files (x86)\\Battle.net\\Battle.net.exe" --remote-debugging-port={self.debug_port}')
        print("⏳ Ожидание запуска Battle.net...")

        # Ждем, пока Battle.net запустится с debug портом
        for i in range(30):  # Ждем до 30 секунд
            time.sleep(1)
            tabs = []
            try:
                import aiohttp
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                async def check_tabs():
                    async with aiohttp.ClientSession() as session:
                        async with session.get(f'http://localhost:{self.debug_port}/json') as resp:
                            if resp.status == 200:
                                return await resp.json()
                    return []

                tabs = loop.run_until_complete(check_tabs())
                loop.close()

                if tabs:
                    print(f"✅ Battle.net запущен с debug портом! Найдено {len(tabs)} вкладок")
                    return True

            except:
                pass

            if i % 5 == 0:
                print(f"   Ожидание... ({i}/30 сек)")

        print("❌ Battle.net не запустился с debug портом за 30 секунд")
        return False
    
    async def get_tabs(self):
        """Получение списка вкладок/фреймов"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f'http://localhost:{self.debug_port}/json') as resp:
                    if resp.status == 200:
                        tabs = await resp.json()
                        return tabs
                    else:
                        print(f"❌ Ошибка получения вкладок: {resp.status}")
                        return []
        except Exception as e:
            print(f"❌ Ошибка подключения к debug порту: {e}")
            return []
    
    async def find_whisper_tabs(self):
        """Поиск всех вкладок с whisper чатами"""
        tabs = await self.get_tabs()

        if not tabs:
            return []

        print(f"📋 Найдено {len(tabs)} вкладок/фреймов:")

        whisper_tabs = []
        for i, tab in enumerate(tabs):
            url = tab.get('url', '')
            title = tab.get('title', '')
            tab_type = tab.get('type', '')

            print(f"   {i+1}. {title} ({tab_type}) - {url}")

            # Поиск вкладок с whisper чатами (только chat.html, исключаем dark.html)
            if 'resources://social/chat.html#/whisper/' in url:
                whisper_tabs.append(tab)
                print(f"✅ Найдена whisper вкладка: {url}")
            elif 'resources://social/dark.html#/whisper/' in url:
                print(f"⏭️ Пропускаем dark.html вкладку: {url}")

        if whisper_tabs:
            print(f"🎯 Найдено {len(whisper_tabs)} whisper вкладок для мониторинга")
            return whisper_tabs

        # Если whisper вкладки не найдены, ищем любые social вкладки
        for tab in tabs:
            url = tab.get('url', '')
            if 'social' in url.lower():
                print(f"⚠️ Используем social вкладку: {url}")
                return [tab]

        return []
    
    async def connect(self):
        """Подключение ко всем whisper вкладкам Battle.net"""
        print("🔍 Поиск Battle.net процесса...")

        # Проверяем, запущен ли Battle.net с debug портом
        tabs = await self.get_tabs()

        if not tabs:
            print("⚠️ Battle.net не найден или запущен без debug порта")
            if not self.start_battlenet_debug():
                return False

            # Повторная попытка получения вкладок
            await asyncio.sleep(3)
            tabs = await self.get_tabs()

            if not tabs:
                print("❌ Не удалось подключиться к Battle.net")
                return False

        # Поиск всех whisper вкладок
        whisper_tabs = await self.find_whisper_tabs()

        if not whisper_tabs:
            print("❌ Не найдены whisper вкладки для мониторинга")
            return False

        # Подключение ко всем whisper вкладкам
        successful_connections = 0
        for tab in whisper_tabs:
            connection = WhisperConnection(tab, self.debug_port)
            if await connection.connect():
                self.connections.append(connection)
                successful_connections += 1
            else:
                print(f"⚠️ Не удалось подключиться к {tab.get('url', 'неизвестная вкладка')}")

        if successful_connections > 0:
            self.connected = True
            print(f"✅ Успешно подключено к {successful_connections} whisper вкладкам!")
            return True
        else:
            print("❌ Не удалось подключиться ни к одной whisper вкладке")
            return False
    
    async def inject_javascript(self, js_code):
        """Внедрение JavaScript кода во все подключенные whisper вкладки"""
        if not self.connected or not self.connections:
            print("❌ Нет подключений к whisper вкладкам")
            return False

        successful_injections = 0

        for i, connection in enumerate(self.connections):
            try:
                print(f"💉 Внедрение в вкладку {i+1}: {connection.url}")

                if await connection.inject_javascript(js_code):
                    print(f"✅ JavaScript успешно внедрен в вкладку {i+1}")
                    successful_injections += 1
                else:
                    print(f"❌ Ошибка внедрения в вкладку {i+1}")

            except Exception as e:
                print(f"❌ Исключение при внедрении в вкладку {i+1}: {e}")

        if successful_injections > 0:
            print(f"🎉 JavaScript успешно внедрен в {successful_injections}/{len(self.connections)} вкладок")
            return True
        else:
            print("❌ Не удалось внедрить JavaScript ни в одну вкладку")
            return False
    
    async def disconnect(self):
        """Отключение от всех whisper вкладок"""
        disconnected_count = 0

        for connection in self.connections:
            try:
                await connection.disconnect()
                disconnected_count += 1
            except Exception as e:
                print(f"⚠️ Ошибка отключения от {connection.url}: {e}")

        self.connections.clear()
        self.connected = False
        print(f"🔌 Отключено от {disconnected_count} whisper вкладок")


# Тестирование клиента
async def test_cdp_client():
    client = BattleNetCDPClient()
    
    if await client.connect():
        print("🧪 Тестирование подключения...")
        
        # Простой тест JavaScript
        test_js = "console.log('CDP Test: JavaScript injection works!');"
        await client.inject_javascript(test_js)
        
        await asyncio.sleep(2)
        await client.disconnect()
    else:
        print("❌ Не удалось подключиться для тестирования")


if __name__ == "__main__":
    asyncio.run(test_cdp_client())
